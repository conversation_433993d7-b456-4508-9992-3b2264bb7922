# Noti Project Gemini Configuration

This file provides context for the Gemini AI assistant to understand the Noti project.

## Project Overview

Noti is a desktop application built with Electron and Vue.js. It appears to be a note-taking or personal knowledge management application with features like book reading, backup, and sync.

## Technologies

*   **Frameworks**: Electron, Vue.js
*   **Language**: TypeScript
*   **Build Tool**: Vite
*   **Database**: (Likely SQLite, need to confirm)
*   **Styling**: (Likely CSS, need to confirm if any frameworks like Tailwind are used)

## Key Directories

*   `electron/`: Contains the main Electron process code.
*   `src/`: Contains the Vue.js application source code.
*   `public/`: Contains public assets.
*   `scripts/`: Contains various scripts for development and testing.
*   `bookdocs/`: Contains documentation related to the book reading feature.
*   `BugFixesMD/`: Contains documentation related to bug fixes.
*   `Code Documentation/`: Contains various code documentation files.

This information should help <PERSON> understand the project and provide better assistance.
