import { ipcMain } from 'electron';
import { getDatabase } from '../database/database';
import BookParser from '../lib/book-parser';
import bookContentStorage from '../lib/book-content-storage';
import { allAsync, runAsync } from '../database/database-utils';
import * as path from 'path';
import * as fs from 'fs/promises';

export interface BookContentRequest {
    bookId: number;
    pageNumber?: number;
    chapterId?: number;
}

export interface ReadingProgressUpdate {
    bookId: number;
    currentPage: number;
    percentage: number;
    position?: string;
}

export interface ReadingSessionStart {
    bookId: number;
    startPage: number;
}

export function registerBookContentHandlers(): void {
    const db = getDatabase();

    // Parse and store book content
    ipcMain.handle('book-content:parse', async (event, bookId: number, filePath: string) => {
        try {
            // Parse the book file
            const parsedBook = await BookParser.parseBook(filePath);
            
            // Store the parsed content
            await bookContentStorage.storeBookContent(bookId, parsedBook);
            
            // Extract and store cover image if available
            const coverImage = await BookParser.extractCoverImage(parsedBook);
            if (coverImage) {
                // Store cover image in media_files table
                const result = await runAsync(db, `
                    INSERT INTO media_files (file_name, mime_type, file_data, file_size)
                    VALUES (?, ?, ?, ?)
                `, [`book_${bookId}_cover.jpg`, 'image/jpeg', coverImage, coverImage.length]);
                
                // Update book with cover image
                await runAsync(db, `
                    UPDATE books SET cover_image_id = last_insert_rowid() WHERE id = ?
                `, [bookId]);
            }
            
            return { success: true, totalPages: parsedBook.totalPages };
        } catch (error) {
            console.error('Error parsing book content:', error);
            throw error;
        }
    });

    // Get book content metadata
    ipcMain.handle('book-content:get-metadata', async (event, bookId: number) => {
        try {
            return await bookContentStorage.getBookContent(bookId);
        } catch (error) {
            console.error('Error getting book metadata:', error);
            throw error;
        }
    });

    // Get chapters list
    ipcMain.handle('book-content:get-chapters', async (event, bookId: number) => {
        try {
            return await bookContentStorage.getChapters(bookId);
        } catch (error) {
            console.error('Error getting chapters:', error);
            throw error;
        }
    });

    // Get single page content
    ipcMain.handle('book-content:get-page', async (event, request: BookContentRequest) => {
        try {
            const { bookId, pageNumber } = request;
            if (!pageNumber) {
                throw new Error('Page number is required');
            }
            return await bookContentStorage.getPage(bookId, pageNumber);
        } catch (error) {
            console.error('Error getting page content:', error);
            throw error;
        }
    });

    // Get page range
    ipcMain.handle('book-content:get-page-range', async (event, bookId: number, startPage: number, endPage: number) => {
        try {
            return await bookContentStorage.getPageRange(bookId, startPage, endPage);
        } catch (error) {
            console.error('Error getting page range:', error);
            throw error;
        }
    });

    // Update reading progress
    ipcMain.handle('book-content:update-progress', async (event, progress: ReadingProgressUpdate) => {
        try {
            const { bookId, currentPage, percentage, position } = progress;
            
            await runAsync(db, `
                UPDATE books SET
                    reading_position = ?,
                    reading_percentage = ?,
                    last_read_at = CURRENT_TIMESTAMP,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `, [position || `page:${currentPage}`, percentage, bookId]);
            
            return { success: true };
        } catch (error) {
            console.error('Error updating reading progress:', error);
            throw error;
        }
    });

    // Start reading session
    ipcMain.handle('book-content:start-session', async (event, session: ReadingSessionStart) => {
        try {
            const { bookId, startPage } = session;
            
            const result = await runAsync(db, `
                INSERT INTO book_reading_sessions (book_id, start_time, start_page)
                VALUES (?, CURRENT_TIMESTAMP, ?)
            `, [bookId, startPage]);
            
            return { success: true, sessionId: result.lastID };
        } catch (error) {
            console.error('Error starting reading session:', error);
            throw error;
        }
    });

    // End reading session
    ipcMain.handle('book-content:end-session', async (event, sessionId: number, endPage: number) => {
        try {
            // Get session start info
            const session = await allAsync(db, `
                SELECT book_id, start_time, start_page
                FROM book_reading_sessions
                WHERE id = ?
            `, [sessionId]);
            
            if (session.length === 0) {
                throw new Error('Session not found');
            }
            
            const { book_id, start_time, start_page } = session[0];
            const pagesRead = Math.abs(endPage - start_page) + 1;
            const startTimestamp = new Date(start_time).getTime();
            const durationSeconds = Math.floor((Date.now() - startTimestamp) / 1000);
            
            // Update session
            await runAsync(db, `
                UPDATE book_reading_sessions SET
                    end_time = CURRENT_TIMESTAMP,
                    end_page = ?,
                    pages_read = ?,
                    duration_seconds = ?
                WHERE id = ?
            `, [endPage, pagesRead, durationSeconds, sessionId]);
            
            // Update total reading time in books table
            await runAsync(db, `
                UPDATE books SET
                    total_reading_time = total_reading_time + ?
                WHERE id = ?
            `, [durationSeconds, book_id]);
            
            return { success: true, pagesRead, durationSeconds };
        } catch (error) {
            console.error('Error ending reading session:', error);
            throw error;
        }
    });

    // Get reading sessions
    ipcMain.handle('book-content:get-sessions', async (event, bookId: number) => {
        try {
            return await allAsync(db, `
                SELECT * FROM book_reading_sessions
                WHERE book_id = ?
                ORDER BY start_time DESC
                LIMIT 50
            `, [bookId]);
        } catch (error) {
            console.error('Error getting reading sessions:', error);
            throw error;
        }
    });

    // Search in book
    ipcMain.handle('book-content:search', async (event, bookId: number, query: string) => {
        try {
            return await bookContentStorage.searchBookContent(bookId, query);
        } catch (error) {
            console.error('Error searching book content:', error);
            throw error;
        }
    });

    // Delete book content
    ipcMain.handle('book-content:delete', async (event, bookId: number) => {
        try {
            await bookContentStorage.deleteBookContent(bookId);
            return { success: true };
        } catch (error) {
            console.error('Error deleting book content:', error);
            throw error;
        }
    });

    // Check if book has content
    ipcMain.handle('book-content:has-content', async (event, bookId: number) => {
        try {
            const result = await allAsync(db, `
                SELECT has_content FROM books WHERE id = ?
            `, [bookId]);
            
            return result.length > 0 && result[0].has_content === 1;
        } catch (error) {
            console.error('Error checking book content:', error);
            throw error;
        }
    });

    // Import book file
    ipcMain.handle('book-content:import-file', async (event, bookId: number, filePath: string) => {
        try {
            // Verify file exists
            await fs.access(filePath);
            
            // Get file info
            const stats = await fs.stat(filePath);
            const fileExt = path.extname(filePath).toLowerCase().slice(1);
            
            // Update book record with file info
            await runAsync(db, `
                UPDATE books SET
                    book_file_path = ?,
                    book_file_format = ?,
                    file_size = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `, [filePath, fileExt, stats.size, bookId]);
            
            // Parse and store content
            const parsedBook = await BookParser.parseBook(filePath);
            await bookContentStorage.storeBookContent(bookId, parsedBook);
            
            return { 
                success: true, 
                format: fileExt,
                totalPages: parsedBook.totalPages,
                fileSize: stats.size
            };
        } catch (error) {
            console.error('Error importing book file:', error);
            throw error;
        }
    });
}