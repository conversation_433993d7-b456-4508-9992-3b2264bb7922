# Book Reading System - Database Schema Design

## Overview

This document outlines the comprehensive database schema design for implementing a full book reading system in Noti. The system will support importing books (PDF, EPUB, etc.), storing their content, managing bookmarks, annotations, and reading progress.

## Core Principles

1. **Content Storage**: Books will be stored in their entirety in the database, not just as file references
2. **Granular Access**: Content will be broken down into chapters, pages, or sections for efficient loading
3. **Rich Annotations**: Support for highlighting, notes, and bookmarks tied to specific locations
4. **Sync-Ready**: All data structures are designed with the existing sync system in mind
5. **Media Integration**: Leverages existing media storage system for images and embedded content

## New Database Tables

### 1. `book_content` Table

Stores the actual content of imported books.

```sql
CREATE TABLE IF NOT EXISTS book_content (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    format TEXT NOT NULL CHECK (format IN ('pdf', 'epub', 'mobi', 'azw3', 'fb2', 'cbz')),
    original_file BLOB,  -- Store original file for re-processing if needed
    file_hash TEXT NOT NULL,  -- SHA-256 hash for deduplication
    total_pages INTEGER,
    total_chapters INTEGER,
    table_of_contents TEXT,  -- JSON structure of TOC
    metadata TEXT,  -- JSON: publisher, edition, etc.
    text_content TEXT,  -- Full searchable text (for search functionality)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
    UNIQUE(book_id)  -- One content per book
);

CREATE INDEX idx_book_content_book_id ON book_content(book_id);
CREATE INDEX idx_book_content_file_hash ON book_content(file_hash);
```

### 2. `book_chapters` Table

Breaks down book content into manageable chapters/sections.

```sql
CREATE TABLE IF NOT EXISTS book_chapters (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    chapter_number INTEGER NOT NULL,
    title TEXT,
    start_page INTEGER,
    end_page INTEGER,
    content_html TEXT,  -- Rendered HTML content
    content_text TEXT,  -- Plain text for search
    "order" INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
    UNIQUE(book_id, chapter_number)
);

CREATE INDEX idx_book_chapters_book_id ON book_chapters(book_id);
CREATE INDEX idx_book_chapters_order ON book_chapters(book_id, "order");
```

### 3. `book_pages` Table

Stores individual pages for page-based formats (PDF).

```sql
CREATE TABLE IF NOT EXISTS book_pages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    chapter_id INTEGER,
    page_number INTEGER NOT NULL,
    content_html TEXT,  -- Rendered HTML
    content_text TEXT,  -- Extracted text
    page_image_id INTEGER,  -- Reference to media_files for page image
    width INTEGER,  -- Original page dimensions
    height INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
    FOREIGN KEY (chapter_id) REFERENCES book_chapters(id) ON DELETE SET NULL,
    FOREIGN KEY (page_image_id) REFERENCES media_files(id) ON DELETE SET NULL,
    UNIQUE(book_id, page_number)
);

CREATE INDEX idx_book_pages_book_id ON book_pages(book_id);
CREATE INDEX idx_book_pages_page_number ON book_pages(book_id, page_number);
```

### 4. `book_bookmarks` Table

Manages user bookmarks within books.

```sql
CREATE TABLE IF NOT EXISTS book_bookmarks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    user_id INTEGER,  -- For future multi-user support
    name TEXT,
    description TEXT,
    page_number INTEGER,
    chapter_id INTEGER,
    position_data TEXT,  -- JSON: {"paragraph": 3, "sentence": 2, "word": 5}
    color TEXT,
    "order" INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
    FOREIGN KEY (chapter_id) REFERENCES book_chapters(id) ON DELETE SET NULL
);

CREATE INDEX idx_book_bookmarks_book_id ON book_bookmarks(book_id);
CREATE INDEX idx_book_bookmarks_page ON book_bookmarks(book_id, page_number);
```

### 5. `book_annotations` Table

Stores highlights, notes, and annotations.

```sql
CREATE TABLE IF NOT EXISTS book_annotations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    note_id INTEGER,  -- Link to full note if expanded
    user_id INTEGER,  -- For future multi-user support
    type TEXT NOT NULL CHECK (type IN ('highlight', 'note', 'underline', 'comment')),
    content TEXT,  -- The annotation text/comment
    selected_text TEXT,  -- The text that was highlighted
    color TEXT,
    page_number INTEGER,
    chapter_id INTEGER,
    -- Precise position data
    start_position TEXT,  -- JSON: {"page": 1, "paragraph": 2, "word": 10, "char": 5}
    end_position TEXT,    -- JSON: {"page": 1, "paragraph": 2, "word": 15, "char": 3}
    -- For PDF annotations
    pdf_coords TEXT,  -- JSON: {"x1": 100, "y1": 200, "x2": 300, "y2": 250}
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
    FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE SET NULL,
    FOREIGN KEY (chapter_id) REFERENCES book_chapters(id) ON DELETE SET NULL
);

CREATE INDEX idx_book_annotations_book_id ON book_annotations(book_id);
CREATE INDEX idx_book_annotations_page ON book_annotations(book_id, page_number);
CREATE INDEX idx_book_annotations_type ON book_annotations(type);
```

### 6. `book_reading_sessions` Table

Tracks reading sessions and progress.

```sql
CREATE TABLE IF NOT EXISTS book_reading_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    user_id INTEGER,  -- For future multi-user support
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    start_page INTEGER,
    end_page INTEGER,
    pages_read INTEGER,
    duration_seconds INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);

CREATE INDEX idx_reading_sessions_book_id ON book_reading_sessions(book_id);
CREATE INDEX idx_reading_sessions_time ON book_reading_sessions(start_time DESC);
```

### 7. `book_search_index` Table

Full-text search index for book content.

```sql
CREATE VIRTUAL TABLE IF NOT EXISTS book_search_index USING fts5(
    book_id,
    page_number,
    chapter_id,
    content,
    tokenize='porter unicode61'
);
```

## Modifications to Existing Tables

### Updates to `books` Table

Add fields for reading progress and state:

```sql
-- Add new columns to existing books table
ALTER TABLE books ADD COLUMN has_content BOOLEAN DEFAULT 0;
ALTER TABLE books ADD COLUMN last_read_at TIMESTAMP;
ALTER TABLE books ADD COLUMN reading_position TEXT;  -- JSON: current position data
ALTER TABLE books ADD COLUMN reading_percentage REAL DEFAULT 0;
ALTER TABLE books ADD COLUMN total_reading_time INTEGER DEFAULT 0;  -- in seconds
ALTER TABLE books ADD COLUMN bookmark_count INTEGER DEFAULT 0;
ALTER TABLE books ADD COLUMN annotation_count INTEGER DEFAULT 0;
```

### Updates to `notes` Table

Already has `book_id` field, but we'll use it to link notes to specific annotations:

```sql
-- Add reference to annotation if note was created from one
ALTER TABLE notes ADD COLUMN annotation_id INTEGER REFERENCES book_annotations(id) ON DELETE SET NULL;
```

## Key Design Decisions

### 1. Content Storage Strategy

- **Original File**: Stored as BLOB for potential re-processing
- **Parsed Content**: Stored as both HTML (for rendering) and plain text (for search)
- **Chunking**: Content split into chapters/pages for performance
- **Images**: Stored separately in media_files table, referenced by ID

### 2. Position Tracking

- **Flexible System**: JSON-based position data supports different formats
- **Multiple Levels**: Page, chapter, paragraph, word, and character-level precision
- **PDF Coordinates**: Special handling for PDF annotations with x,y coordinates

### 3. Sync Considerations

- **File Hash**: Ensures deduplication across devices
- **Granular Sync**: Individual annotations/bookmarks can sync independently
- **Conflict Resolution**: Timestamps and position data help resolve conflicts

### 4. Performance Optimizations

- **Indexes**: Strategic indexes on frequently queried fields
- **FTS5**: Full-text search for finding content quickly
- **Lazy Loading**: Chapter/page structure allows loading content on demand

## Migration Strategy

1. **Phase 1**: Create new tables without breaking existing functionality
2. **Phase 2**: Update UI to support book import and reading
3. **Phase 3**: Implement annotation and bookmark features
4. **Phase 4**: Integrate with sync system
5. **Phase 5**: Migrate any existing book data to new structure

## Data Flow

1. **Import**: User selects file → Parse with Foliate.js → Store in book_content → Split into chapters/pages
2. **Reading**: Load book → Fetch current chapter/page → Track progress → Update reading_sessions
3. **Annotation**: Select text → Create annotation → Link to position → Optionally create full note
4. **Sync**: Export annotations/bookmarks → Include in manifest → Sync position data

## Security Considerations

- **File Validation**: Verify file types and sizes before import
- **Content Sanitization**: Clean HTML content before storage
- **Access Control**: Prepare for future multi-user scenarios
- **Data Integrity**: Foreign key constraints maintain consistency
