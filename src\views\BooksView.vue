<template>
    <div class="books-view">
        <BookHeader ref="bookHeaderRef" :searchQuery="searchQuery" @search="handleSearch" @createNew="createNewBook" />
        <div class="books-content">
            <p class="placeholder-text" v-if="loading">
                Loading your books...
            </p>
            <p class="placeholder-text" v-else-if="error">
                {{ error }}
            </p>
            <div class="empty-collection-message" v-else-if="!searchQuery && !allBooks.length && !recentBooks.length">
                <p class="placeholder-text">Your book collection is empty.</p>
                <p class="placeholder-action">Click the <strong>"New Book"</strong> button above to add your first book!
                </p>
            </div>
            <p class="placeholder-text" v-else-if="searchQuery && !filteredBooks.length && !searching">
                No books found matching "{{ searchQuery }}".
            </p>
            <p class="placeholder-text" v-else-if="searching">
                Searching for "{{ searchQuery }}"...
            </p>

            <div v-if="!searchQuery && !loading && (recentBooks.length || allBooks.length)" class="books-sections">
                <section v-if="recentBooks.length" class="books-section">
                    <h2 class="section-title">Recently Added Books</h2>
                    <div class="books-grid">
                        <BookCard v-for="book in recentBooks" :key="`recent-${book.id}`" :book="book"
                            @click="openBookDetails(book)" @openNote="openNote" @openReader="openBookReader" />
                    </div>
                </section>

                <section v-if="allBooks.length" class="books-section">
                    <h2 class="section-title">All Books</h2>
                    <div class="books-grid">
                        <BookCard v-for="book in allBooks" :key="`all-${book.id}`" :book="book"
                            @click="openBookDetails(book)" @openNote="openNote" @openReader="openBookReader" />
                    </div>
                </section>
            </div>

            <div v-else-if="searchQuery && filteredBooks.length && !searching" class="books-sections">
                <section class="books-section">
                    <h2 class="section-title">Search Results</h2>
                    <div class="books-grid">
                        <BookCard v-for="book in filteredBooks" :key="`search-${book.id}`" :book="book"
                            @click="openBookDetails(book)" @openNote="openNote" @openReader="openBookReader" />
                    </div>
                </section>
            </div>
        </div>

        <!-- Add Book Modal -->
        <AddBookModal v-if="showAddBookModal" :searchQuery="addBookSearchQuery" :searchResults="addBookSearchResults"
            :loading="addBookSearchLoading" :error="addBookSearchError" :hasSearched="addBookHasSearched"
            :showTooShortMessage="addBookShowTooShortMessage" @close="closeAddBookModal" @add-book="addNewBook"
            @add-manually="showAddManuallyModalHandler" @edit-book="editBookHandler"
            @search-update="handleAddBookSearchUpdate" /> <!-- Add Book Manually Modal -->
        <AddBookManuallyModal v-if="showAddManuallyModal && !bookToEdit" :bookData="null" @close="closeAddManuallyModal"
            @add-book="addManualBook" @go-back="goBackToAddBookModal" />

        <EditBookModal v-if="showEditModal && editBookData" :bookData="editBookData"
            :userCustomCover="customCovers.get(editBookData?.olid || editBookData?.key)" @close="showEditModal = false"
            @save-book="handleEditBook" @go-back="goBackFromEditModal"
            @cover-changed="(cover) => handleCoverChanged(editBookData?.olid || editBookData?.key, cover)" />

        <!-- Book Details Modal -->
        <BookDetailsModal v-if="showBookDetailsModal && selectedBook" :book="selectedBook"
            @close="closeBookDetailsModal" @update-book="updateBook" @delete-book="deleteBook" @open-note="openNote"
            @create-note="createNote" @open-reader="openBookReader" />

        <!-- Loading overlay for book details fetching -->
        <Teleport to="body">
            <div v-if="fetchingBookDetails" class="loading-overlay">
                <div class="loading-spinner">
                    <div class="spinner"></div>
                    <p>Loading book details...</p>
                </div>
            </div>
        </Teleport>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, onUnmounted, watch } from 'vue';
import BookHeader from '../components/books/BookHeader.vue';
import BookCard from '../components/books/BookCard.vue';
import AddBookModal from '../components/modals/AddBookModal.vue';
import AddBookManuallyModal from '../components/modals/AddBookManuallyModal.vue';
import EditBookModal from '../components/modals/EditBookModal.vue';
import BookDetailsModal from '../components/modals/BookDetailsModal.vue';
import { useElectronAPI } from '../useElectronAPI';
import { useRouter, useRoute } from 'vue-router';
import { useBooksKeybinds } from '../composables/useBooksKeybinds';
import { usePageLoadMonitoring } from '../composables/usePageLoadMonitoring';
import type { BookWithNoteCount, BookSearchResult, Note } from '../types/electron-api';

// Utility function to extract only the year part from a publication date string
const extractYearFromString = (dateString: string | null): string | null => {
    if (!dateString) return null;

    // Check for BCE/BC years
    const bceMatch = dateString.match(/(\d+)\s*(?:BCE|BC)(?:\.|)/i);
    if (bceMatch && bceMatch[1]) {
        // Convert BCE year to negative number
        return `-${bceMatch[1]}`;
    }

    // Check for already negative years (already in numeric BCE format)
    const negativeYearMatch = dateString.match(/^-(\d+)$/);
    if (negativeYearMatch) {
        return dateString; // Already in the right format
    }

    // Modern years: try to extract a 4-digit year pattern
    const yearMatch = dateString.match(/\b(19|20)\d{2}\b/);
    if (yearMatch && yearMatch[0]) {
        return yearMatch[0];
    }

    // If we couldn't find a year with regex, try to parse it as a date
    try {
        const date = new Date(dateString);
        if (!isNaN(date.getTime())) {
            return date.getFullYear().toString();
        }
    } catch (e) {
        // Parsing failed, return the original string
    }

    return dateString;
};

export default defineComponent({
    name: 'BooksView',
    components: {
        BookHeader,
        BookCard,
        AddBookModal,
        AddBookManuallyModal,
        EditBookModal,
        BookDetailsModal
    },
    setup() {
        const db = useElectronAPI();
        const router = useRouter();
        const route = useRoute();

        // Setup keybinds
        const { setupBookFunctions, activate: activateKeybinds, deactivate: deactivateKeybinds } = useBooksKeybinds();

        // Page load monitoring
        const { autoRecordPageMounted } = usePageLoadMonitoring();
        autoRecordPageMounted('Books');

        // Reactive state
        const searchQuery = ref('');
        const showAddBookModal = ref(false);
        const showAddManuallyModal = ref(false);
        const showBookDetailsModal = ref(false);
        const selectedBook = ref<BookWithNoteCount | null>(null);
        const bookToEdit = ref<BookSearchResult | null>(null);
        const books = ref<BookWithNoteCount[]>([]);
        const loading = ref(true);
        const searching = ref(false);
        const error = ref('');
        const fetchingBookDetails = ref(false); // New loading state for book details
        const customCovers = ref(new Map());
        const showEditModal = ref(false);
        const editBookData = ref<BookSearchResult | null>(null);

        // Ref for BookHeader component to access search
        const bookHeaderRef = ref<InstanceType<typeof BookHeader> | null>(null);

        // Add Book Modal search state persistence
        const addBookSearchQuery = ref('');
        const addBookSearchResults = ref<BookSearchResult[]>([]);
        const addBookSearchLoading = ref(false);
        const addBookSearchError = ref('');
        const addBookHasSearched = ref(false);
        const addBookShowTooShortMessage = ref(false);

        // Computed properties
        const recentBooks = computed(() => {
            // Since books are already sorted by created_at DESC in the database,
            // we can just take the first 4 books to get the most recently added ones
            return books.value?.slice(0, 4) || [];
        });

        const allBooks = computed(() => books.value);

        const filteredBooks = computed(() => {
            if (!searchQuery.value) return [];

            const query = searchQuery.value.toLowerCase();
            return books.value.filter(book =>
                book.title.toLowerCase().includes(query) ||
                (book.author && book.author.toLowerCase().includes(query)) ||
                (book.isbn && book.isbn.toLowerCase().includes(query)) ||
                (book.genres && book.genres.toLowerCase().includes(query))
            );
        });

        // Methods
        const loadBooks = async (): Promise<void> => {
            try {
                loading.value = true;
                error.value = '';
                const booksData = await db.books.getBooksWithMetadata();

                // Process publication dates and preserve custom covers
                books.value = booksData.map(book => {
                    const processedBook = {
                        ...book,
                        publication_date: book.publication_date ? extractYearFromString(book.publication_date) : null
                    };

                    // Check if this book has a custom cover stored in memory
                    // Try multiple possible keys since the book might be stored under different identifiers
                    const possibleKeys = [
                        book.olid,
                        (book as any).key,
                        book.id?.toString(),
                        // Also try the key without the /works/ prefix if it exists
                        (book as any).key ? (book as any).key.replace('/works/', '') : null
                    ].filter(Boolean);

                    let customCover = null;
                    let foundKey = null;

                    for (const key of possibleKeys) {
                        if (customCovers.value.has(key)) {
                            customCover = customCovers.value.get(key);
                            foundKey = key;
                            break;
                        }
                    }

                    if (customCover) {
                        console.log('🎨 Applying stored custom cover to book:', book.title, 'using key:', foundKey);
                        processedBook.cover_url = customCover;
                        // Mark this book to prevent future cover overrides
                        (processedBook as any).hasCustomCover = true;
                    }

                    return processedBook;
                });

                // Clean up customCovers map - remove entries that don't correspond to any loaded book
                const validKeys = new Set<string>();
                books.value.forEach(book => {
                    // Collect all possible keys for this book
                    const possibleKeys = [
                        book.olid,
                        (book as any).key,
                        book.id?.toString(),
                        (book as any).key ? (book as any).key.replace('/works/', '') : null
                    ].filter(Boolean);
                    
                    possibleKeys.forEach(key => validKeys.add(key));
                });

                // Remove entries from customCovers that don't match any loaded book
                const keysToRemove: string[] = [];
                customCovers.value.forEach((_, key) => {
                    if (!validKeys.has(key)) {
                        keysToRemove.push(key);
                    }
                });

                keysToRemove.forEach(key => {
                    customCovers.value.delete(key);
                    console.log('🧹 Cleaned up unused custom cover with key:', key);
                });

                if (keysToRemove.length > 0) {
                    console.log(`🧹 Cleaned up ${keysToRemove.length} unused custom covers`);
                }
            } catch (err) {
                console.error('Failed to load books:', err);
                error.value = 'Failed to load books. Please try again.';
            } finally {
                loading.value = false;
            }
        };

        const handleSearch = async (query: string) => {
            searchQuery.value = query;

            if (query.trim()) {
                try {
                    searching.value = true;
                    error.value = '';
                    // For now, we'll just use local filtering
                    // In the future, this could trigger a database search
                    await new Promise(resolve => setTimeout(resolve, 300)); // Simulate search delay
                } catch (err) {
                    console.error('Search failed:', err);
                    error.value = 'Search failed. Please try again.';
                } finally {
                    searching.value = false;
                }
            }
        };

        const createNewBook = () => {
            showAddBookModal.value = true;
        };

        const closeAddBookModal = () => {
            showAddBookModal.value = false;
            // Clear search state when modal is closed (not when transitioning to edit)
            clearAddBookSearchState();
        };

        const clearAddBookSearchState = () => {
            addBookSearchQuery.value = '';
            addBookSearchResults.value = [];
            addBookSearchLoading.value = false;
            addBookSearchError.value = '';
            addBookHasSearched.value = false;
            addBookShowTooShortMessage.value = false;
        };

        const handleAddBookSearchUpdate = (searchState: {
            query: string;
            results: BookSearchResult[];
            loading: boolean;
            error: string;
            hasSearched: boolean;
            showTooShortMessage: boolean;
        }) => {
            addBookSearchQuery.value = searchState.query;
            addBookSearchResults.value = searchState.results;
            addBookSearchLoading.value = searchState.loading;
            addBookSearchError.value = searchState.error;
            addBookHasSearched.value = searchState.hasSearched;
            addBookShowTooShortMessage.value = searchState.showTooShortMessage;
        };

        const addNewBook = async (bookData: BookSearchResult) => {
            error.value = '';

            // Create a temporary book entry with loading state
            const tempBook: BookWithNoteCount = {
                id: Date.now(), // Temporary ID
                title: bookData.title,
                author: bookData.author_name?.join(', ') || null,
                isbn: bookData.isbn_primary || null,
                cover_url: bookData.cover_url || null,
                publication_date: bookData.first_publish_year ? bookData.first_publish_year.toString() : null,
                description: bookData.description || null,
                genres: bookData.genres || null,
                notesCount: 0,
                isLoading: true // Add loading flag
            };

            // Add to local state immediately
            books.value.unshift(tempBook);

            try {
                // Actually add the book to the database in the background
                const addedBook = await db.books.addFromOpenLibrary(bookData);
                console.log(`✓ Book "${addedBook.title}" added successfully to database`);

                // Clear search state immediately after successful addition
                // This prevents the search results from persisting when the modal is reopened
                clearAddBookSearchState();

                // After successful addition, reload the books to get the real data
                setTimeout(async () => {
                    try {
                        await loadBooks(); // This will replace the temp book with real data
                        console.log(`✓ Books reloaded, loading state cleared for "${addedBook.title}"`);
                    } catch (reloadError) {
                        console.error('Failed to reload books after adding:', reloadError);
                        // Still remove the loading state even if reload fails
                        const tempIndex = books.value.findIndex(book => book.id === tempBook.id);
                        if (tempIndex !== -1) {
                            books.value[tempIndex].isLoading = false;
                        }
                    }
                }, 3000); // Increased delay to 3 seconds to allow more time for cover download
            } catch (err: any) {
                console.error('Failed to add book:', err);

                // Remove the temporary book from the list on error
                const tempIndex = books.value.findIndex(book => book.id === tempBook.id);
                if (tempIndex !== -1) {
                    books.value.splice(tempIndex, 1);
                }

                // Show error message
                if (err && err.message && err.message.includes('already exists in your library')) {
                    error.value = 'This book is already in your library';
                } else {
                    error.value = 'Failed to add book. Please try again.';
                }
                // Note: We don't clear search state on error so users can retry with the same search
            }
        };

        const showAddManuallyModalHandler = () => {
            showAddBookModal.value = false;
            bookToEdit.value = null; // Reset any editing book data
            showAddManuallyModal.value = true;
        };

        // Enhanced editBookHandler that fetches detailed book information
        const editBookHandler = async (book: BookSearchResult) => {
            // Don't clear search state - just hide the modal
            showAddBookModal.value = false;
            fetchingBookDetails.value = true; // Show loading state

            console.log('EditBookHandler - Original book data:', JSON.stringify(book, null, 2));

            // Fetch detailed book information using the same function as normal add process
            if (book.olid) {
                try {
                    console.log(`Fetching detailed book info for OLID: ${book.olid}`);
                    const detailedBook = await db.books.getDetailsFromOpenLibrary(book.olid);
                    console.log('EditBookHandler - Detailed book data:', JSON.stringify(detailedBook, null, 2));

                    // Merge the search result with detailed data
                    editBookData.value = {
                        ...book, // Start with search result
                        ...detailedBook, // Override with detailed data
                        // Preserve search result properties that might be better
                        cover_url: book.cover_url || detailedBook.cover_url,
                        author_name: book.author_name || (detailedBook.author ? [detailedBook.author] : undefined),
                        first_publish_year: book.first_publish_year || (detailedBook.publication_date ? parseInt(detailedBook.publication_date) : undefined),
                        // Convert detailed book properties to search result format
                        page_count: detailedBook.page_count,
                        rating: detailedBook.rating || 0,
                        // Ensure we have both formats for compatibility
                        genres: book.genres || detailedBook.genres,
                        isbn_primary: book.isbn_primary || detailedBook.isbn,
                        // Fix: Preserve language field from search result, fallback to detailed book language
                        language: book.language || (detailedBook.language ? [detailedBook.language] : undefined)
                    } as BookSearchResult;

                    console.log('EditBookHandler - Final merged book data:', JSON.stringify(editBookData.value, null, 2));
                } catch (fetchError: unknown) {
                    console.warn('Failed to fetch detailed book info, using search result:', fetchError);
                    editBookData.value = {
                        ...book,
                        description: book.description || '',
                        page_count: null,
                        rating: 0,
                        genres: book.genres || (Array.isArray(book.subject) && book.subject.length > 0
                            ? book.subject.slice(0, 3).join(', ')
                            : ''),
                        // Fix: Ensure language field is preserved in error fallback case
                        language: book.language
                    } as BookSearchResult;

                    // Show a non-blocking error message
                    const errorMessage = fetchError instanceof Error ? fetchError.message : String(fetchError);
                    if (!errorMessage?.includes('timeout')) {
                        error.value = 'Could not load detailed book information, but you can still edit the available data.';
                        setTimeout(() => {
                            if (error.value.includes('Could not load detailed book information')) {
                                error.value = '';
                            }
                        }, 5000);
                    }
                }
            } else {
                console.log('No OLID available, using search result data');
                editBookData.value = {
                    ...book,
                    description: book.description || '',
                    page_count: null,
                    rating: 0,
                    genres: book.genres || (Array.isArray(book.subject) && book.subject.length > 0
                        ? book.subject.slice(0, 3).join(', ')
                        : ''),
                    // Fix: Ensure language field is preserved in fallback case
                    language: book.language
                } as BookSearchResult;
            }

            fetchingBookDetails.value = false; // Hide loading state
            console.log('🎨 Checking for custom cover in customCovers Map');
            const customCoverKey = editBookData.value.olid || editBookData.value.key;
            const storedCustomCover = customCovers.value.get(customCoverKey);
            console.log('🎨 Custom cover key:', customCoverKey);
            console.log('🎨 Stored custom cover exists:', storedCustomCover ? 'YES' : 'NO');

            if (storedCustomCover) {
                console.log('🎨 Applying stored custom cover to editBookData');
                editBookData.value.cover_url = storedCustomCover;
            }
            showEditModal.value = true; // Show edit modal instead of add manually modal
        };

        const closeAddManuallyModal = () => {
            showAddManuallyModal.value = false;
            bookToEdit.value = null; // Reset the book being edited
        };

        const goBackToAddBookModal = () => {
            showAddManuallyModal.value = false;
            bookToEdit.value = null; // Reset the book being edited
            showAddBookModal.value = true;
        };

        const goBackFromEditModal = () => {
            showEditModal.value = false;
            editBookData.value = null; // Reset the book being edited
            // Restore AddBookModal with preserved search state
            showAddBookModal.value = true;
        };

        const addManualBook = async (bookData: any) => {
            error.value = '';

            // Preserve custom cover information
            const hasCustomCover = bookData.hasCustomCover || bookData.userModifiedCover;
            const customCoverUrl = hasCustomCover ? bookData.cover_url : null;
            const preventCoverOverride = bookData.preventCoverOverride || false;

            console.log('🎨 addManualBook - hasCustomCover:', hasCustomCover);
            console.log('🎨 addManualBook - customCoverUrl length:', customCoverUrl?.length);
            console.log('🎨 addManualBook - preventCoverOverride:', preventCoverOverride);

            // Create a clean, serializable copy of the book data
            const cleanBookData = {
                title: bookData.title,
                author: bookData.author || null,
                isbn: bookData.isbn || null,
                publication_date: bookData.publication_date ? extractYearFromString(bookData.publication_date) : null,
                language: bookData.language || null,
                page_count: bookData.page_count || null,
                genres: bookData.genres || null,
                rating: bookData.rating || null,
                description: bookData.description || null,
                cover_url: bookData.cover_url || null,
                olid: bookData.olid || null // Preserve OpenLibrary ID if present
            };

            // Add a temporary book with loading state to provide immediate feedback
            const tempBook: BookWithNoteCount = {
                id: Date.now(), // Temporary ID
                title: cleanBookData.title,
                author: cleanBookData.author,
                isbn: cleanBookData.isbn,
                cover_url: cleanBookData.cover_url,
                publication_date: cleanBookData.publication_date ? cleanBookData.publication_date.toString() : null,
                description: cleanBookData.description,
                genres: cleanBookData.genres,
                notesCount: 0,
                isLoading: true // Add loading flag
            };

            // Add to local state immediately
            books.value.unshift(tempBook);

            try {

                let addedBook;

                // If the book came from editing an OpenLibrary search result
                if (bookData.fromOpenLibrary && bookData.openLibraryData) {
                    const originalData = bookData.openLibraryData;

                    // Create a hybrid object that has both BookSearchResult properties and edited data
                    const hybridData: BookSearchResult = {
                        title: cleanBookData.title,
                        author_name: cleanBookData.author ? [cleanBookData.author] : originalData.author_name,
                        isbn: originalData.isbn,
                        cover_i: originalData.cover_i,
                        cover_edition_key: originalData.cover_edition_key,
                        first_publish_year: cleanBookData.publication_date ?
                            parseInt(cleanBookData.publication_date) : originalData.first_publish_year,
                        language: cleanBookData.language ? [cleanBookData.language] : originalData.language,
                        edition_count: originalData.edition_count,
                        key: originalData.key,
                        subject: originalData.subject,
                        publisher: originalData.publisher,
                        publish_year: originalData.publish_year,
                        oclc: originalData.oclc,
                        lccn: originalData.lccn,
                        olid: originalData.olid,
                        description: cleanBookData.description || originalData.description,
                        genres: cleanBookData.genres || originalData.genres,
                        // IMPORTANT: Use custom cover if it exists, otherwise use the original cover
                        cover_url: hasCustomCover ? customCoverUrl : (originalData.cover_url || cleanBookData.cover_url),
                        isbn_primary: cleanBookData.isbn || originalData.isbn_primary
                    };

                    // Use the OpenLibrary API to add the book with all the rich metadata
                    addedBook = await db.books.addFromOpenLibrary(hybridData);
                } else {
                    // Standard manual book creation
                    addedBook = await db.books.create(cleanBookData, true);
                }

                // Store custom cover information in the customCovers map using ALL possible identifiers
                if (hasCustomCover && customCoverUrl && addedBook) {
                    const possibleKeys = [
                        addedBook.olid,
                        (addedBook as any).key,
                        addedBook.id?.toString(),
                        bookData.olid,
                        bookData.key
                    ].filter(Boolean);

                    console.log('🎨 Storing custom cover for newly added book with keys:', possibleKeys);

                    // Store the custom cover under all possible keys to ensure it's found later
                    possibleKeys.forEach(key => {
                        customCovers.value.set(key, customCoverUrl);
                        console.log('🎨 Stored cover under key:', key);
                    });
                }

                // After successful addition, reload the books to get the real data
                setTimeout(async () => {
                    try {
                        await loadBooks(); // This will replace the temp book with real data and apply custom covers
                        console.log(`✓ Books reloaded after manual book addition: "${cleanBookData.title}"`);
                    } catch (reloadError) {
                        console.error('Failed to reload books after adding:', reloadError);
                        // Still remove the loading state even if reload fails
                        const tempIndex = books.value.findIndex(book => book.id === tempBook.id);
                        if (tempIndex !== -1) {
                            books.value[tempIndex].isLoading = false;
                        }
                    }
                }, 1500); // Allow some time for potential cover download

            } catch (err: any) {
                console.error('Failed to add manual book:', err);

                // Remove the temporary book from the list on error
                const tempIndex = books.value.findIndex(book => book.id === tempBook.id);
                if (tempIndex !== -1) {
                    books.value.splice(tempIndex, 1);
                }

                // Show appropriate error message
                if (err && err.message && err.message.includes('already exists in your library')) {
                    error.value = 'This book is already in your library.';
                } else {
                    error.value = 'Failed to add book. Please try again.';
                }
            }
        };

        const openBookDetails = (book: BookWithNoteCount) => {
            // Don't open details for books that are still loading or have temporary IDs
            if (book.isLoading || (book.id && book.id > 1000000000000)) { // Timestamp IDs are > 1 trillion
                console.log('Cannot open details for book that is still loading or has temporary ID');
                return;
            }

            selectedBook.value = book;
            showBookDetailsModal.value = true;
        };

        const closeBookDetailsModal = () => {
            showBookDetailsModal.value = false;
            selectedBook.value = null;
        };

        const updateBook = async (updatedBook: BookWithNoteCount) => {
            // Update the book in the local state
            const index = books.value.findIndex(book => book.id === updatedBook.id);
            if (index !== -1) {
                books.value[index] = updatedBook;
            }
            selectedBook.value = updatedBook;

            // Reload books to ensure we have the latest data including cover updates
            await loadBooks();

            // Update selectedBook with the fresh data
            const refreshedBook = books.value.find(book => book.id === updatedBook.id);
            if (refreshedBook) {
                selectedBook.value = refreshedBook;
            }
        };

        const openNote = (note: Note) => {
            // Navigate to notes view and open the specific note
            console.log('Opening note:', note);

            // Check if note has a valid ID before navigation
            if (!note.id) {
                console.error('Cannot open note: Note ID is missing or undefined');
                error.value = 'Cannot open note: Note ID is missing. Please try refreshing the page.';
                return;
            }

            closeBookDetailsModal();

            // Navigate to notes view with the note ID as a query parameter
            router.push({
                name: 'Notes',
                query: { noteId: note.id.toString() }
            });
        };

        const openBookReader = (book: BookWithNoteCount) => {
            // Don't open reader for books that are loading or have temporary IDs
            if (book.isLoading || (book.id && book.id > 1000000000000)) {
                console.log('Cannot open reader for book that is still loading or has temporary ID');
                return;
            }

            // Check if book has a valid ID before navigation
            if (!book.id) {
                console.error('Cannot open reader: Book ID is missing or undefined');
                error.value = 'Cannot open reader: Book ID is missing. Please try refreshing the page.';
                return;
            }

            console.log('Opening book reader for:', book.title);

            // Navigate to book reader view
            router.push({
                name: 'BookReader',
                params: { bookId: book.id.toString() }
            });
        };

        const createNote = async (bookId: number) => {
            try {
                console.log('Creating note for book:', bookId);

                // Create the note using the new API
                const newNote = await db.notes.createForBook(bookId);
                console.log('Created note:', newNote);

                // Check if the created note has a valid ID before navigation
                if (!newNote.id) {
                    console.error('Cannot navigate to note: Created note ID is missing or undefined');
                    error.value = 'Note was created but cannot be opened. Please try refreshing the page.';
                    closeBookDetailsModal();
                    return;
                }

                // Close the modal
                closeBookDetailsModal();

                // Navigate to notes view with the newly created note
                router.push({
                    name: 'Notes',
                    query: { noteId: newNote.id.toString() }
                });

            } catch (err) {
                console.error('Failed to create note for book:', err);
                error.value = 'Failed to create note. Please try again.';
            }
        };

        const deleteBook = (bookId: number) => {
            // Remove the book from the local state immediately
            const index = books.value.findIndex(book => book.id === bookId);
            if (index !== -1) {
                books.value.splice(index, 1);
            }

            // Close the modal
            closeBookDetailsModal();
        };

        // Configure keybind functions
        setupBookFunctions({
            openAddBookModal: () => {
                showAddBookModal.value = true;
            },
            focusBookSearch: () => {
                if (bookHeaderRef.value) {
                    (bookHeaderRef.value as any).focusSearch();
                }
            },
            openBookDetails: () => {
                // Find first visible book and open its details
                const visibleBooks = searchQuery.value ? filteredBooks.value : allBooks.value;
                if (visibleBooks.length > 0) {
                    openBookDetails(visibleBooks[0]);
                }
            },
            deleteSelectedBook: () => {
                // Delete the currently selected book in the modal
                if (selectedBook.value) {
                    deleteBook(selectedBook.value.id!);
                }
            },
            openBookNote: () => {
                // Find and open the first note of the selected book
                if (selectedBook.value && selectedBook.value.notesCount > 0) {
                    // This would require getting notes for the book first
                    // For now, just create a new note
                    createNote(selectedBook.value.id!);
                }
            },
            createBookNote: () => {
                // Create new note for selected book
                if (selectedBook.value) {
                    createNote(selectedBook.value.id!);
                }
            }
        });

        // Handle action from dashboard
        const handleAddAction = async () => {
            try {
                // Clean URL first
                await router.replace({ query: { ...route.query, action: undefined } });
                
                // Wait for data loading if needed
                if (loading.value) {
                    await new Promise<void>(resolve => {
                        const unwatch = watch(() => loading.value, (isLoading) => {
                            if (!isLoading) {
                                unwatch();
                                resolve();
                            }
                        });
                    });
                }
                
                // Open modal
                showAddBookModal.value = true;
            } catch (error) {
                console.error('Failed to open add book modal from dashboard action:', error);
            }
        };

        // Lifecycle
        onMounted(() => {
            loadBooks().then(() => {
                // Handle action parameter
                const actionParam = route.query.action;
                if (actionParam === 'add') {
                    handleAddAction();
                }
            });
            // Activate keybinds for this view
            activateKeybinds();
        });

        // Clean up timeout on component unmount
        onUnmounted(() => {
            // Deactivate keybinds
            deactivateKeybinds();
            
            if (searchTimeout) {
                clearTimeout(searchTimeout);
                searchTimeout = null;
            }
        });

        // Watch for search query changes with debouncing
        let searchTimeout: number | null = null;
        watch(searchQuery, (newQuery) => {
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }

            if (newQuery.trim()) {
                searchTimeout = window.setTimeout(() => {
                    // Could implement server-side search here if needed
                }, 500);
            }
        });
        
        // Watch for action parameter changes
        watch(() => route.query.action, async (action) => {
            if (action === 'add' && !loading.value) {
                await handleAddAction();
            }
        });

        const handleCoverChanged = (bookId: any, coverUrl: any) => {
            console.log('🎨 BooksView - handleCoverChanged called!');
            console.log('🎨 BooksView - Book ID:', bookId);
            console.log('🎨 BooksView - Cover URL length:', coverUrl?.length);
            console.log('🎨 BooksView - customCovers Map before:', customCovers.value.size);

            customCovers.value.set(bookId, coverUrl);

            console.log('🎨 BooksView - customCovers Map after:', customCovers.value.size);
            console.log('🎨 BooksView - Successfully stored custom cover');
        };

        // In BooksView.vue, update the handleEditBook method:

        const handleEditBook = async (bookData: any) => {
            try {
                console.log('🔄 BooksView - handleEditBook called with:', JSON.stringify(bookData, null, 2));

                // Close the edit modal
                showEditModal.value = false;
                editBookData.value = null;

                // IMPORTANT: Preserve the custom cover by adding it to the bookData
                const bookKey = bookData.olid || bookData.key;
                const customCover = customCovers.value.get(bookKey);

                if (customCover && bookData.userModifiedCover) {
                    console.log('🎨 Preserving custom cover in book data before adding to database');
                    bookData.cover_url = customCover;
                    // Also ensure the backend knows this is a custom cover
                    bookData.hasCustomCover = true;
                    // Mark this book as having a user-modified cover to prevent API overrides
                    bookData.preventCoverOverride = true;
                }

                // Add the book using the same logic as addManualBook
                await addManualBook(bookData);

            } catch (err) {
                console.error('Failed to handle edit book:', err);
                error.value = 'Failed to save book. Please try again.';
            }
        };

        return {
            searchQuery,
            recentBooks,
            allBooks,
            filteredBooks,
            showAddBookModal,
            showAddManuallyModal,
            showEditModal, // Add this
            editBookData,
            loading,
            searching,
            error,
            fetchingBookDetails, // New loading state
            customCovers,
            // Add Book Modal search state
            addBookSearchQuery,
            addBookSearchResults,
            addBookSearchLoading,
            addBookSearchError,
            addBookHasSearched,
            addBookShowTooShortMessage,
            handleSearch,
            createNewBook,
            closeAddBookModal,
            addNewBook,
            showAddManuallyModalHandler,
            closeAddManuallyModal,
            goBackToAddBookModal,
            goBackFromEditModal,
            addManualBook,
            openBookDetails,
            closeBookDetailsModal,
            showBookDetailsModal,
            selectedBook,
            bookToEdit,
            editBookHandler,
            handleEditBook, // Add this
            handleCoverChanged,
            updateBook,
            deleteBook,
            openNote,
            openBookReader,
            createNote,
            handleAddBookSearchUpdate
        };
    }
});
</script>

<style scoped>
.books-view {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    font-family: 'Montserrat', sans-serif;
}

.books-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 20px 20px 20px 20px;
    overflow-y: auto;
    scroll-snap-type: none;
    /* Remove snap scrolling to keep titles visible */
    position: relative;
    z-index: 1;
    isolation: isolate;
    scrollbar-gutter: stable;
    /* Reserves space for the scrollbar */
    box-sizing: border-box;
    /* Include padding in the width calculation */
    /* Add a slight delay to content reflow */
    transition: all 0.3s ease 0.1s;
}

/* Custom scrollbar styling for books content */
.books-content::-webkit-scrollbar {
    width: 8px;
    position: absolute;
    right: 0;
}

.books-content::-webkit-scrollbar-track {
    background: var(--color-scrollbar-track);
    border-radius: 0;
}

.books-content::-webkit-scrollbar-thumb {
    background: var(--color-scrollbar-thumb);
    border-radius: 4px;
}

.books-content::-webkit-scrollbar-thumb:hover {
    background: var(--color-scrollbar-thumb-hover);
}

.books-sections {
    width: 100%;
}

.books-section {
    margin-bottom: 40px;
    width: 100%;
    scroll-margin-top: 20px;
}

.section-title {
    color: var(--color-text-primary);
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 20px;
    margin-left: 10px;
    scroll-margin-top: 20px;
    /* Ensures title is visible when scrolled to */
    padding-top: 10px;
    /* Add some space above the title */
}

.placeholder-text {
    color: var(--color-text-secondary);
    font-size: 16px;
    margin-top: 40px;
    align-self: center;
    word-wrap: break-word;
    word-break: break-word;
    max-width: 100%;
    text-align: center;
    padding: 0 20px;
    box-sizing: border-box;
}

.empty-collection-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    width: 100%;
}

.empty-collection-message .placeholder-text {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 18px;
}

.placeholder-action {
    color: var(--color-text-primary);
    font-size: 16px;
    margin-top: 5px;
    font-family: 'Montserrat', sans-serif;
}

.placeholder-action strong {
    color: var(--color-primary);
    font-weight: 600;
}

/* FLEXBOX GRID SOLUTION - REPLACES CSS GRID */
.books-grid {
    /* FLEXBOX INSTEAD OF CSS GRID */
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    width: 100%;
    /* This will actually animate smoothly */
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Make each book card responsive with flex */
.books-grid :deep(.book) {
    /* Start with a base width that matches your grid */
    flex: 0 0 calc(16.666% - 17px);
    /* ~6 cards per row when sidebar open */
    min-width: 280px;
    /* Minimum card width */
    max-width: 400px;
    /* Maximum card width */

    /* Smooth transition for flex-basis changes */
    transition: flex-basis 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
        transform 0.2s ease;
}

/* Responsive breakpoints that will smoothly transition */
@media (min-width: 2400px) {
    .books-grid :deep(.book) {
        flex: 0 0 calc(14.28% - 17px);
        /* ~7 cards when sidebar closed */
    }
}

@media (max-width: 2000px) {
    .books-grid :deep(.book) {
        flex: 0 0 calc(20% - 16px);
        /* ~5 cards */
    }
}

@media (max-width: 1600px) {
    .books-grid :deep(.book) {
        flex: 0 0 calc(25% - 15px);
        /* ~4 cards */
    }
}

@media (max-width: 1200px) {
    .books-grid :deep(.book) {
        flex: 0 0 calc(33.333% - 14px);
        /* ~3 cards */
    }
}

@media (max-width: 900px) {
    .books-grid :deep(.book) {
        flex: 0 0 calc(50% - 10px);
        /* ~2 cards */
    }
}

@media (max-width: 600px) {
    .books-grid :deep(.book) {
        flex: 0 0 100%;
        /* 1 card */
    }
}

/* Enhanced hover effect */
.books-grid :deep(.book:hover:not(.loading-wave)) {
    transform: translateY(-3px) scale(1.02);
    transition: transform 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Alternative approach: Use CSS Container Queries if you want more precise control */
@supports (container-type: inline-size) {
    .books-grid {
        container-type: inline-size;
    }

    @container (min-width: 2400px) {
        .books-grid :deep(.book) {
            flex: 0 0 calc(14.28% - 17px);
        }
    }

    @container (max-width: 2000px) {
        .books-grid :deep(.book) {
            flex: 0 0 calc(20% - 16px);
        }
    }
}

/* Loading overlay styles */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--color-modal-overlay);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 20000;
    /* Higher than modal */
}

.loading-spinner {
    background-color: var(--color-modal-bg);
    border-radius: 12px;
    padding: 32px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    box-shadow: 0 8px 24px var(--color-card-shadow);
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--color-border-secondary);
    border-top: 3px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loading-spinner p {
    margin: 0;
    color: var(--color-text-primary);
    font-family: 'Montserrat', sans-serif;
    font-size: 14px;
}
</style>