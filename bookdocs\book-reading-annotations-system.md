# Book Reading System - Annotations & Notes Integration

## Overview

This document details the deep integration between the book reading system and Not<PERSON>'s existing notes infrastructure. It covers how annotations, bookmarks, and highlights can seamlessly transform into full notes while maintaining their connection to specific book locations.

## Core Concepts

### 1. Annotation Types

```typescript
enum AnnotationType {
  HIGHLIGHT = 'highlight',      // Simple text highlighting
  UNDERLINE = 'underline',      // Underlined text
  NOTE = 'note',               // Annotation with attached note
  COMMENT = 'comment'          // Margin comment
}

enum BookmarkType {
  SIMPLE = 'simple',           // Basic position marker
  NAMED = 'named',            // Bookmark with custom name
  CHAPTER = 'chapter',         // Chapter bookmark
  QUOTE = 'quote'             // Bookmark with excerpt
}
```

### 2. Annotation-Note Relationship

```
┌──────────────────────┐
│  Book Annotation     │
├──────────────────────┤
│ - Quick highlight    │
│ - Selected text      │
│ - Position data      │─────┐ Can expand to
│ - Color              │     │
│ - Short comment      │     │
└──────────────────────┘     │
                              ▼
┌──────────────────────┐
│   Full Note          │
├──────────────────────┤
│ - Rich text content  │
│ - Tags               │
│ - Media attachments  │
│ - Links to book      │
│ - Original context   │
└──────────────────────┘
```

## Database Design

### 1. Enhanced Notes Table

Add fields to link notes to book annotations:

```sql
-- Existing notes table will be altered to include these fields:
ALTER TABLE notes 
ADD COLUMN annotation_id INTEGER REFERENCES book_annotations(id) ON DELETE SET NULL,
ADD COLUMN reading_context TEXT;  -- JSON: chapter, page, surrounding text
```

### 2. Annotation-Note Link Table

For many-to-many relationships (one note can reference multiple annotations), this table is proposed in the main database schema document. For details, refer to [Book Reading System - Database Schema Design](book-reading-database-schema.md).


## Implementation Architecture

### 1. Annotation Creation Flow

```typescript
// src/lib/annotation-manager.ts
export class AnnotationManager {
  async createAnnotation(
    bookId: number,
    selection: TextSelection,
    type: AnnotationType,
    options: AnnotationOptions = {}
  ): Promise<Annotation> {
    // 1. Save the annotation
    const annotation = await window.db.annotations.create({
      book_id: bookId,
      type,
      selected_text: selection.text,
      color: options.color || this.getDefaultColor(type),
      page_number: selection.page,
      chapter_id: selection.chapterId,
      start_position: selection.startPosition,
      end_position: selection.endPosition,
      content: options.comment || ''
    })
    
    // 2. If user wants to create a note
    if (options.createNote) {
      const note = await this.createNoteFromAnnotation(annotation)
      
      // 3. Link them
      await window.db.annotations.linkToNote(annotation.id, note.id)
    }
    
    // 4. Update UI
    this.applyAnnotationToDOM(annotation)
    
    // 5. Trigger sync
    this.queueSync(annotation)
    
    return annotation
  }
  
  async createNoteFromAnnotation(
    annotation: Annotation
  ): Promise<Note> {
    // Get book context
    const book = await window.db.books.getById(annotation.book_id)
    const chapter = annotation.chapter_id 
      ? await window.db.chapters.getById(annotation.chapter_id)
      : null
    
    // Generate note title
    const title = this.generateNoteTitle(annotation, book, chapter)
    
    // Create note content with context
    const content = this.generateNoteContent(annotation, book)
    
    // Create the note
    const note = await window.db.notes.create({
      title,
      content: content.markdown,
      html_content: content.html,
      book_id: annotation.book_id,
      annotation_id: annotation.id,
      folder_id: await this.getBookNotesFolder(book),
      type: 'text',
      reading_context: JSON.stringify({
        chapter: chapter?.title,
        page: annotation.page_number,
        selected_text: annotation.selected_text,
        position: annotation.start_position
      })
    })
    
    return note
  }
  
  private generateNoteTitle(
    annotation: Annotation,
    book: Book,
    chapter?: Chapter
  ): string {
    const date = new Date().toLocaleDateString()
    const location = chapter ? chapter.title : `Page ${annotation.page_number}`
    
    // Use first few words of selection
    const excerpt = annotation.selected_text
      .split(' ')
      .slice(0, 5)
      .join(' ')
    
    return `${book.title} - ${location} - "${excerpt}..." (${date})`
  }
  
  private generateNoteContent(
    annotation: Annotation,
    book: Book
  ): { html: string, markdown: string } {
    const markdown = `
# Note on "${book.title}"

## Highlighted Text

> ${annotation.selected_text}

## My Thoughts

${annotation.content || '_(Add your thoughts here)_'}

---

_Location: ${this.formatLocation(annotation)}_
_Created: ${new Date().toLocaleString()}_
    `.trim()
    
    const html = marked.parse(markdown)
    
    return { html, markdown }
  }
}
```

### 2. Bookmark System

```typescript
// src/lib/bookmark-manager.ts
export class BookmarkManager {
  async createBookmark(
    bookId: number,
    position: BookPosition,
    options: BookmarkOptions = {}
  ): Promise<Bookmark> {
    const bookmark = await window.db.bookmarks.create({
      book_id: bookId,
      name: options.name || this.generateBookmarkName(position),
      description: options.description,
      page_number: position.page,
      chapter_id: position.chapterId,
      position_data: JSON.stringify(position),
      color: options.color || '#4285F4',
      order: await this.getNextOrder(bookId)
    })
    
    // Option to create a reading note
    if (options.createReadingNote) {
      await this.createReadingNote(bookmark)
    }
    
    return bookmark
  }
  
  async createReadingNote(bookmark: Bookmark): Promise<Note> {
    const book = await window.db.books.getById(bookmark.book_id)
    const context = await this.getBookmarkContext(bookmark)
    
    const note = await window.db.notes.create({
      title: `Reading Note: ${book.title} - ${bookmark.name}`,
      content: `
# Reading Progress

**Book:** ${book.title}
**Location:** ${bookmark.name}
**Date:** ${new Date().toLocaleDateString()}

## Context

${context.text}

## Thoughts


      `.trim(),
      book_id: bookmark.book_id,
      type: 'text'
    })
    
    return note
  }
  
  // Quick bookmarks with keyboard shortcuts
  async quickBookmark(bookId: number): Promise<Bookmark> {
    const position = await this.getCurrentPosition(bookId)
    const number = await this.getBookmarkCount(bookId) + 1
    
    return this.createBookmark(bookId, position, {
      name: `Bookmark ${number}`,
      color: this.getNextColor()
    })
  }
}
```

### 3. Advanced Annotation Features

#### 3.1 Multi-Highlight Consolidation

```typescript
class MultiHighlightManager {
  private pendingHighlights: Map<number, Annotation[]> = new Map()
  
  async addHighlight(bookId: number, annotation: Annotation) {
    if (!this.pendingHighlights.has(bookId)) {
      this.pendingHighlights.set(bookId, [])
    }
    
    this.pendingHighlights.get(bookId)!.push(annotation)
    
    // Show consolidation UI
    this.showConsolidationButton()
  }
  
  async consolidateIntoNote(bookId: number): Promise<Note> {
    const highlights = this.pendingHighlights.get(bookId) || []
    if (highlights.length === 0) return
    
    // Sort by position
    highlights.sort((a, b) => 
      a.start_position.offset - b.start_position.offset
    )
    
    // Create consolidated note
    const book = await window.db.books.getById(bookId)
    const title = `Study Notes: ${book.title} (${highlights.length} highlights)`
    
    const content = this.generateConsolidatedContent(highlights, book)
    
    const note = await window.db.notes.create({
      title,
      content: content.markdown,
      html_content: content.html,
      book_id: bookId,
      type: 'text'
    })
    
    // Link all annotations to the note
    for (const highlight of highlights) {
      await window.db.annotations.linkToNote(highlight.id, note.id)
    }
    
    // Clear pending
    this.pendingHighlights.delete(bookId)
    
    return note
  }
  
  private generateConsolidatedContent(
    highlights: Annotation[],
    book: Book
  ): { html: string, markdown: string } {
    let markdown = `# Study Notes: ${book.title}\n\n`
    markdown += `_${highlights.length} highlights collected_\n\n`
    
    // Group by chapter if available
    const byChapter = this.groupByChapter(highlights)
    
    for (const [chapterTitle, chapterHighlights] of byChapter) {
      markdown += `## ${chapterTitle}\n\n`
      
      for (const highlight of chapterHighlights) {
        markdown += `> ${highlight.selected_text}\n`
        markdown += `_Page ${highlight.page_number}_\n`
        
        if (highlight.content) {
          markdown += `\n**Note:** ${highlight.content}\n`
        }
        
        markdown += `\n---\n\n`
      }
    }
    
    return {
      markdown,
      html: marked.parse(markdown)
    }
  }
}
```

#### 3.2 Smart Note Templates

```typescript
interface NoteTemplate {
  id: string
  name: string
  description: string
  template: string
  variables: string[]
}

const BOOK_NOTE_TEMPLATES: NoteTemplate[] = [
  {
    id: 'chapter-summary',
    name: 'Chapter Summary',
    description: 'Summarize key points from a chapter',
    template: `
# Chapter Summary: {{chapterTitle}}

**Book:** {{bookTitle}}
**Author:** {{bookAuthor}}
**Date:** {{date}}

## Key Points

1. 
2. 
3. 

## Important Quotes

{{#highlights}}
> {{text}}
> _Page {{page}}_

{{/highlights}}

## Personal Reflection


## Questions for Further Thought

1. 
2. 

## Action Items

- [ ] 
    `,
    variables: ['chapterTitle', 'bookTitle', 'bookAuthor', 'date', 'highlights']
  },
  {
    id: 'book-review',
    name: 'Book Review',
    description: 'Write a comprehensive book review',
    template: `
# Book Review: {{bookTitle}}

**Author:** {{bookAuthor}}
**Genre:** {{bookGenre}}
**Read:** {{startDate}} - {{endDate}}
**Rating:** ★★★★☆

## Summary


## What I Liked


## What Could Be Better


## Key Takeaways

{{#bookmarks}}
- {{name}} (p. {{page}})
{{/bookmarks}}

## Memorable Quotes

{{#highlights}}
> {{text}}
> _Page {{page}}_

{{/highlights}}

## Would I Recommend?


## Similar Books

1. 
2. 
    `,
    variables: ['bookTitle', 'bookAuthor', 'bookGenre', 'startDate', 'endDate', 'bookmarks', 'highlights']
  },
  {
    id: 'reading-journal',
    name: 'Daily Reading Journal',
    description: 'Track daily reading progress and thoughts',
    template: `
# Reading Journal: {{date}}

## Today's Reading

**Book:** {{bookTitle}}
**Pages:** {{startPage}} - {{endPage}} ({{pagesRead}} pages)
**Time:** {{readingTime}} minutes
**Location:** {{readingLocation}}

## Summary


## Thoughts & Reactions


## New Vocabulary

- **Word**: Definition
- 

## Connections

_How does this connect to other books/ideas?_


## Tomorrow's Goal


---
_Reading Streak: {{readingStreak}} days_
    `,
    variables: ['date', 'bookTitle', 'startPage', 'endPage', 'pagesRead', 'readingTime', 'readingLocation', 'readingStreak']
  }
]

// Template engine
class NoteTemplateEngine {
  async createNoteFromTemplate(
    templateId: string,
    bookId: number,
    context: any = {}
  ): Promise<Note> {
    const template = BOOK_NOTE_TEMPLATES.find(t => t.id === templateId)
    if (!template) throw new Error('Template not found')
    
    // Gather variables
    const variables = await this.gatherVariables(template, bookId, context)
    
    // Render template
    const content = this.renderTemplate(template.template, variables)
    
    // Create note
    const note = await window.db.notes.create({
      title: this.renderTemplate(template.name, variables),
      content,
      html_content: marked.parse(content),
      book_id: bookId,
      type: 'text'
    })
    
    return note
  }
  
  private async gatherVariables(
    template: NoteTemplate,
    bookId: number,
    context: any
  ): Promise<Record<string, any>> {
    const book = await window.db.books.getById(bookId)
    const variables: Record<string, any> = {
      bookTitle: book.title,
      bookAuthor: book.author,
      bookGenre: book.genres,
      date: new Date().toLocaleDateString(),
      ...context
    }
    
    // Gather highlights if needed
    if (template.variables.includes('highlights')) {
      const highlights = await window.db.annotations.getByBook(bookId, {
        type: 'highlight',
        limit: context.highlightLimit || 10
      })
      
      variables.highlights = highlights.map(h => ({
        text: h.selected_text,
        page: h.page_number,
        note: h.content
      }))
    }
    
    // Gather bookmarks if needed
    if (template.variables.includes('bookmarks')) {
      const bookmarks = await window.db.bookmarks.getByBook(bookId)
      variables.bookmarks = bookmarks.map(b => ({
        name: b.name,
        page: b.page_number
      }))
    }
    
    return variables
  }
  
  private renderTemplate(
    template: string,
    variables: Record<string, any>
  ): string {
    // Simple mustache-style template rendering
    return template.replace(/{{(#?)([^}]+)}}/g, (match, isLoop, key) => {
      if (isLoop) {
        // Handle loops
        const items = variables[key] || []
        const loopContent = template
          .substring(
            template.indexOf(match) + match.length,
            template.indexOf(`{{/${key}}}`)
          )
        
        return items.map(item => 
          this.renderTemplate(loopContent, { ...variables, ...item })
        ).join('')
      } else {
        // Simple variable replacement
        return variables[key] || ''
      }
    })
  }
}
```

### 4. UI Components Integration

#### 4.1 Annotation Toolbar

```vue
<!-- src/components/reader/AnnotationToolbar.vue -->
<template>
  <div 
    v-if="selection"
    class="annotation-toolbar"
    :style="toolbarStyle"
  >
    <!-- Quick Actions -->
    <div class="toolbar-section quick-actions">
      <button
        v-for="color in highlightColors"
        :key="color"
        class="color-button"
        :style="{ backgroundColor: color }"
        @click="createHighlight(color)"
        :title="`Highlight in ${getColorName(color)}`"
      />
    </div>
    
    <div class="toolbar-divider" />
    
    <!-- Annotation Types -->
    <div class="toolbar-section annotation-types">
      <button
        class="toolbar-button"
        @click="createAnnotation('underline')"
        title="Underline"
      >
        <UnderlineIcon />
      </button>
      
      <button
        class="toolbar-button"
        @click="createAnnotation('note')"
        title="Add Note"
      >
        <NoteIcon />
      </button>
      
      <button
        class="toolbar-button"
        @click="createBookmark()"
        title="Bookmark"
      >
        <BookmarkIcon />
      </button>
    </div>
    
    <div class="toolbar-divider" />
    
    <!-- Advanced Actions -->
    <div class="toolbar-section advanced-actions">
      <button
        class="toolbar-button"
        @click="createFullNote()"
        title="Create Full Note"
      >
        <ExpandIcon />
        <span>Expand to Note</span>
      </button>
      
      <button
        v-if="hasMultipleHighlights"
        class="toolbar-button"
        @click="consolidateHighlights()"
        title="Consolidate Highlights"
      >
        <MergeIcon />
        <span>{{ pendingHighlights.length }}</span>
      </button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { useAnnotationManager } from '@/composables/useAnnotationManager'
import { useTextSelection } from '@/composables/useTextSelection'

const props = defineProps<{
  bookId: number
}>()

const { selection, clearSelection } = useTextSelection()
const { createAnnotation, createHighlight } = useAnnotationManager(props.bookId)

const highlightColors = [
  '#FFEB3B', // Yellow
  '#81C784', // Green
  '#64B5F6', // Blue
  '#FFB74D', // Orange
  '#F06292'  // Pink
]

const toolbarStyle = computed(() => {
  if (!selection.value) return {}
  
  const rect = selection.value.rect
  return {
    position: 'fixed',
    left: `${rect.left + rect.width / 2}px`,
    top: `${rect.top - 48}px`,
    transform: 'translateX(-50%)'
  }
})

const createFullNote = async () => {
  const annotation = await createAnnotation('note', {
    createNote: true,
    openInEditor: true
  })
  
  // Navigate to note editor
  router.push({
    name: 'NoteEditor',
    params: { noteId: annotation.note_id }
  })
}
</script>
```

#### 4.2 Annotation Sidebar

```vue
<!-- src/components/reader/AnnotationSidebar.vue -->
<template>
  <div class="annotation-sidebar">
    <!-- Filter Controls -->
    <div class="sidebar-header">
      <h3>Annotations & Notes</h3>
      
      <div class="filter-controls">
        <select v-model="filterType" class="filter-select">
          <option value="all">All Types</option>
          <option value="highlight">Highlights</option>
          <option value="note">Notes</option>
          <option value="bookmark">Bookmarks</option>
        </select>
        
        <input
          v-model="searchQuery"
          type="text"
          placeholder="Search annotations..."
          class="search-input"
        />
      </div>
    </div>
    
    <!-- Grouped Annotations -->
    <div class="annotations-list">
      <div
        v-for="group in groupedAnnotations"
        :key="group.id"
        class="annotation-group"
      >
        <h4 class="group-title">{{ group.title }}</h4>
        
        <AnnotationCard
          v-for="annotation in group.items"
          :key="annotation.id"
          :annotation="annotation"
          @click="navigateToAnnotation(annotation)"
          @edit="editAnnotation(annotation)"
          @delete="deleteAnnotation(annotation)"
          @expand="expandToNote(annotation)"
        />
      </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="sidebar-footer">
      <button
        class="action-button"
        @click="exportAnnotations()"
      >
        <ExportIcon />
        Export All
      </button>
      
      <button
        class="action-button primary"
        @click="createStudyGuide()"
      >
        <DocumentIcon />
        Create Study Guide
      </button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { useAnnotations } from '@/composables/useAnnotations'
import { useBookNotes } from '@/composables/useBookNotes'

const props = defineProps<{
  bookId: number
}>()

const { annotations, bookmarks, deleteAnnotation } = useAnnotations(props.bookId)
const { createStudyGuide, exportAnnotations } = useBookNotes(props.bookId)

const filterType = ref('all')
const searchQuery = ref('')

const filteredItems = computed(() => {
  let items = [...annotations.value, ...bookmarks.value]
  
  // Apply type filter
  if (filterType.value !== 'all') {
    items = items.filter(item => item.type === filterType.value)
  }
  
  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    items = items.filter(item => 
      item.selected_text?.toLowerCase().includes(query) ||
      item.content?.toLowerCase().includes(query) ||
      item.name?.toLowerCase().includes(query)
    )
  }
  
  return items
})

const groupedAnnotations = computed(() => {
  // Group by chapter or page
  const groups = new Map()
  
  for (const item of filteredItems.value) {
    const key = item.chapter_id || `page-${item.page_number}`
    const title = item.chapter_title || `Page ${item.page_number}`
    
    if (!groups.has(key)) {
      groups.set(key, {
        id: key,
        title,
        items: []
      })
    }
    
    groups.get(key).items.push(item)
  }
  
  return Array.from(groups.values())
})
</script>
```

### 5. Export & Import

#### 5.1 Annotation Export Formats

```typescript
class AnnotationExporter {
  async exportAnnotations(
    bookId: number,
    format: 'markdown' | 'json' | 'html' | 'docx'
  ): Promise<Blob> {
    const book = await window.db.books.getById(bookId)
    const annotations = await window.db.annotations.getByBook(bookId)
    const bookmarks = await window.db.bookmarks.getByBook(bookId)
    
    switch (format) {
      case 'markdown':
        return this.exportAsMarkdown(book, annotations, bookmarks)
      case 'json':
        return this.exportAsJSON(book, annotations, bookmarks)
      case 'html':
        return this.exportAsHTML(book, annotations, bookmarks)
      case 'docx':
        return this.exportAsDocx(book, annotations, bookmarks)
    }
  }
  
  private async exportAsMarkdown(
    book: Book,
    annotations: Annotation[],
    bookmarks: Bookmark[]
  ): Promise<Blob> {
    let content = `# Annotations for "${book.title}"\n`
    content += `**Author:** ${book.author}\n`
    content += `**Exported:** ${new Date().toLocaleString()}\n\n`
    
    // Export bookmarks
    if (bookmarks.length > 0) {
      content += `## Bookmarks\n\n`
      for (const bookmark of bookmarks) {
        content += `- **${bookmark.name}** (Page ${bookmark.page_number})\n`
        if (bookmark.description) {
          content += `  ${bookmark.description}\n`
        }
      }
      content += `\n`
    }
    
    // Export annotations grouped by type
    const byType = this.groupByType(annotations)
    
    for (const [type, items] of byType) {
      content += `## ${this.formatType(type)}\n\n`
      
      for (const annotation of items) {
        content += `### Page ${annotation.page_number}\n\n`
        content += `> ${annotation.selected_text}\n\n`
        
        if (annotation.content) {
          content += `**Note:** ${annotation.content}\n\n`
        }
        
        content += `---\n\n`
      }
    }
    
    return new Blob([content], { type: 'text/markdown' })
  }
}
```

#### 5.2 Study Guide Generation

```typescript
class StudyGuideGenerator {
  async generateStudyGuide(
    bookId: number,
    options: StudyGuideOptions = {}
  ): Promise<Note> {
    const book = await window.db.books.getById(bookId)
    const annotations = await window.db.annotations.getByBook(bookId)
    const bookmarks = await window.db.bookmarks.getByBook(bookId)
    const readingSessions = await window.db.readingSessions.getByBook(bookId)
    
    // Analyze content
    const analysis = await this.analyzeAnnotations(annotations)
    
    // Generate guide content
    const content = await this.generateContent({
      book,
      annotations,
      bookmarks,
      readingSessions,
      analysis,
      options
    })
    
    // Create study guide note
    const note = await window.db.notes.create({
      title: `Study Guide: ${book.title}`,
      content: content.markdown,
      html_content: content.html,
      book_id: bookId,
      type: 'text',
      folder_id: await this.getStudyGuidesFolder()
    })
    
    // Link all relevant annotations
    for (const annotation of annotations) {
      await window.db.annotations.linkToNote(annotation.id, note.id)
    }
    
    return note
  }
  
  private async analyzeAnnotations(annotations: Annotation[]): Promise<Analysis> {
    // Extract key themes
    const themes = await this.extractThemes(annotations)
    
    // Find most highlighted sections
    const hotspots = this.findHotspots(annotations)
    
    // Generate summary
    const summary = await this.generateSummary(annotations)
    
    return { themes, hotspots, summary }
  }
}
```

## Integration with Existing Features

### 1. Search Integration

```typescript
// Enhanced search to include annotations
class UnifiedSearcher {
  async search(query: string): Promise<SearchResults> {
    const [notes, books, annotations] = await Promise.all([
      this.searchNotes(query),
      this.searchBooks(query),
      this.searchAnnotations(query)
    ])
    
    return {
      notes,
      books,
      annotations: annotations.map(a => ({
        ...a,
        type: 'annotation',
        title: `Annotation in "${a.book_title}"`,
        preview: a.selected_text,
        path: `/books/${a.book_id}/read?annotation=${a.id}`
      }))
    }
  }
}
```

### 2. Dashboard Integration

```typescript
// Add reading insights to dashboard
interface ReadingInsights {
  totalAnnotations: number
  totalBookmarks: number
  recentAnnotations: Annotation[]
  readingStreak: number
  topAnnotatedBooks: Book[]
}

class DashboardEnhancements {
  async getReadingInsights(): Promise<ReadingInsights> {
    const oneWeekAgo = new Date()
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
    
    const [annotations, bookmarks, recentAnnotations] = await Promise.all([
      window.db.annotations.count(),
      window.db.bookmarks.count(),
      window.db.annotations.getRecent(5),
      window.db.books.getTopAnnotated(3)
    ])
    
    return {
      totalAnnotations: annotations,
      totalBookmarks: bookmarks,
      recentAnnotations,
      readingStreak: await this.calculateReadingStreak(),
      topAnnotatedBooks
    }
  }
}
```

### 3. Keyboard Shortcuts

```typescript
const ANNOTATION_KEYBINDS = {
  'cmd+h': 'highlight:yellow',
  'cmd+shift+h': 'highlight:menu',
  'cmd+u': 'underline',
  'cmd+shift+n': 'create:note',
  'cmd+b': 'bookmark:quick',
  'cmd+shift+b': 'bookmark:named',
  'cmd+alt+c': 'consolidate:highlights',
  'cmd+alt+s': 'generate:study-guide'
}
```

## Sync Considerations

### 1. Annotation Sync Format

```json
{
  "type": "annotation",
  "id": "annotation_123",
  "book_id": "book_456",
  "annotation_type": "highlight",
  "selected_text": "This is the highlighted text",
  "position": {
    "start": { "chapter": 3, "paragraph": 12, "offset": 45 },
    "end": { "chapter": 3, "paragraph": 12, "offset": 87 }
  },
  "metadata": {
    "color": "#FFEB3B",
    "created_at": "2024-01-15T10:30:00Z",
    "device_id": "device_abc"
  },
  "linked_note_id": "note_789"
}
```

### 2. Conflict Resolution

```typescript
class AnnotationConflictResolver {
  resolve(local: Annotation, remote: Annotation): Annotation {
    // Same text, different positions - keep both
    if (local.selected_text === remote.selected_text &&
        !this.positionsMatch(local, remote)) {
      return this.createDuplicate(local, remote)
    }
    
    // Different colors - use most recent
    if (local.color !== remote.color) {
      return local.updated_at > remote.updated_at ? local : remote
    }
    
    // Linked notes - merge
    if (local.note_id && remote.note_id && local.note_id !== remote.note_id) {
      return this.mergeWithNotes(local, remote)
    }
    
    // Default: newest wins
    return local.updated_at > remote.updated_at ? local : remote
  }
}
```

## Performance Optimizations

### 1. Annotation Rendering

```typescript
class AnnotationRenderer {
  private renderCache = new Map<string, RenderedAnnotation>()
  private intersectionObserver: IntersectionObserver
  
  constructor() {
    // Only render visible annotations
    this.intersectionObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            this.renderAnnotation(entry.target.dataset.annotationId)
          }
        })
      },
      { rootMargin: '100px' }
    )
  }
  
  async renderChapter(chapterId: number, content: string): Promise<string> {
    const annotations = await this.getAnnotationsForChapter(chapterId)
    
    // Batch DOM operations
    const fragment = document.createDocumentFragment()
    const temp = document.createElement('div')
    temp.innerHTML = content
    
    // Apply annotations in a single pass
    this.applyAnnotations(temp, annotations)
    
    return temp.innerHTML
  }
}
```

### 2. Lazy Loading Notes

```typescript
class LazyNoteLoader {
  async loadAnnotationNote(annotationId: number): Promise<Note | null> {
    // Check if annotation has a linked note
    const annotation = await window.db.annotations.getById(annotationId)
    if (!annotation?.note_id) return null
    
    // Load note on demand
    return window.db.notes.getById(annotation.note_id)
  }
}
```

## Future Enhancements

1. **AI-Powered Summaries**: Use AI to generate chapter summaries from highlights
2. **Social Annotations**: Share and discover annotations from other readers
3. **Smart Recommendations**: Suggest related books based on annotation patterns
4. **Voice Notes**: Add audio annotations while reading
5. **Handwriting Support**: Draw and write directly on pages (especially for PDFs)
