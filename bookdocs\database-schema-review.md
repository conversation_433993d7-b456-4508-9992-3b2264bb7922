# Database Schema Review and Proposed Changes

## Overview
This document summarizes the review of the existing database schema and the proposed extensions for the book reading system. The goal is to ensure the new schema integrates seamlessly with the current Noti database and adheres to established conventions.

## Existing Database Structure (Summary of `electron/main/database/database.ts` and `database-api.ts`)

The current database setup uses SQLite and includes the following key tables:
- `notes`: Stores user notes, linked to folders and optionally to books.
- `folders`: Organizes notes hierarchically, can also be linked to books.
- `books`: Stores basic book metadata (title, author, ISBN, cover URL, etc.).
- `media_files`: Stores paths to media files, including book covers, linked to notes or books.
- `sync_state`, `sync_sessions`, `sync_directory_state`: Tables for managing synchronization.
- Other tables for `recent_items`, `theme_settings`, `exports`, `search_history`, `settings`, `timer_sessions`, `pomodoro_cycles`, `timer_settings`.

The `database-api.ts` provides a clear CRUD interface for `notes`, `folders`, and `books`, along with transaction helpers. `database-hooks.ts` handles change notifications for the sync system.

## Proposed Database Schema Extensions (from `bookdocs/book-reading-database-schema.md` and `bookdocs/BookReader-Settings-Bookmarks-Implementation-Plan.md`)

The book reading feature requires significant extensions to the database to store book content, reading progress, annotations, and settings. The proposed schema introduces several new tables and modifies existing ones.

### New Tables:

1.  **`book_content`**:
    -   Stores the actual content of imported books (BLOB for original file, JSON for TOC/metadata, TEXT for full searchable text).
    -   `book_id` (FK to `books`), `format`, `file_hash`, `total_pages`, `total_chapters`.
    -   Ensures content is stored directly in the database for offline access and efficient parsing.

2.  **`book_chapters`**:
    -   Breaks down book content into manageable chapters/sections for EPUBs.
    -   `book_id` (FK to `books`), `chapter_number`, `title`, `start_page`, `end_page`, `content_html`, `content_text`, `order`.

3.  **`book_pages`**:
    -   Stores individual pages for page-based formats like PDFs.
    -   `book_id` (FK to `books`), `chapter_id` (FK to `book_chapters`), `page_number`, `content_html`, `content_text`, `page_image_id` (FK to `media_files`), `width`, `height`.

4.  **`book_bookmarks`**:
    -   Manages user bookmarks within books.
    -   `book_id` (FK to `books`), `user_id`, `name`, `description`, `page_number`, `chapter_id` (FK to `book_chapters`), `position_data` (JSON for precise location), `color`, `order`.

5.  **`book_annotations`**:
    -   Stores highlights, notes, and annotations.
    -   `book_id` (FK to `books`), `note_id` (FK to `notes` if expanded), `user_id`, `type` (highlight, note, underline, comment), `content`, `selected_text`, `color`, `page_number`, `chapter_id` (FK to `book_chapters`), `start_position` (JSON), `end_position` (JSON), `pdf_coords` (JSON for PDFs).

6.  **`book_reading_sessions`**:
    -   Tracks reading sessions and progress for analytics.
    -   `book_id` (FK to `books`), `user_id`, `start_time`, `end_time`, `start_page`, `end_page`, `pages_read`, `duration_seconds`.

7.  **`book_reading_settings`**:
    -   Stores user-specific reading preferences per book.
    -   `book_id` (FK to `books`), `font_size`, `font_family`, `line_height`, `theme`, `page_width`, `margin`, `flow`, `max_column_count`.

### Virtual Table:

1.  **`book_search_index` (FTS5)**:
    -   Full-text search index for book content.
    -   `book_id`, `page_number`, `chapter_id`, `content`.
    -   Uses `porter unicode61` tokenizer for efficient searching.

### Modifications to Existing Tables:

1.  **`books` Table**:
    -   Add `has_content` (BOOLEAN), `last_read_at` (TIMESTAMP), `reading_position` (TEXT JSON), `reading_percentage` (REAL), `total_reading_time` (INTEGER), `bookmark_count` (INTEGER), `annotation_count` (INTEGER).
    -   Add `book_file_path` (TEXT), `book_file_format` (TEXT), `file_size` (INTEGER) for imported file details.

2.  **`notes` Table**:
    -   Add `annotation_id` (INTEGER FK to `book_annotations`) to link notes directly to their originating annotation.
    -   Add `reading_context` (TEXT JSON) to store contextual information about where the note was created in the book.

## Review Findings and Adherence to Conventions

The proposed schema aligns well with the existing database design principles:
-   **Normalization**: The new tables are well-normalized, avoiding large JSON blobs for structured data where dedicated tables are more appropriate (e.g., `book_chapters`, `book_pages`, `book_annotations`).
-   **Foreign Keys**: Extensive use of `FOREIGN KEY` constraints with `ON DELETE CASCADE` or `ON DELETE SET NULL` ensures data integrity and proper cleanup, consistent with existing tables.
-   **Indexing**: The plan includes strategic indexing (`idx_book_content_book_id`, `idx_book_bookmarks_book_id`, etc.) and the use of `FTS5` for search, which is crucial for performance with large datasets.
-   **Timestamps**: `created_at` and `updated_at` columns are consistently used across new tables, matching the existing convention for tracking record lifecycle.
-   **Data Types**: Appropriate SQLite data types are used for each field.
-   **Modularity**: The new tables are logically grouped and extend the existing `books` and `notes` entities without disrupting their core functionality.
-   **Sync Readiness**: The schema design, particularly the `file_hash` in `book_content` and the granular nature of annotations/bookmarks, supports the incremental and conflict-resolution strategies outlined in `book-reading-sync-integration.md`.

## Conclusion

The proposed database schema is comprehensive, well-designed, and respects the current database architecture and coding standards. It provides a solid foundation for implementing the full book reading system, ensuring data integrity, performance, and seamless integration with existing features like notes and synchronization. The detailed planning documents have effectively translated the feature requirements into a robust database model.
