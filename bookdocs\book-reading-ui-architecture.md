# Book Reading System - UI Architecture & Component Design

## Overview

This document outlines the UI modifications and new components needed for the book reading system. The design builds upon the existing BookReader.vue component and integrates seamlessly with Noti's current architecture.

## Component Architecture

### 1. BookReader.vue Enhancements

#### Current State
- Basic file import interface
- Placeholder reading area
- Simple page navigation

#### Enhanced Architecture

```vue
<template>
  <div class="book-reader">
    <!-- Import Section (Enhanced) -->
    <BookImporter 
      v-if="!hasContent"
      @book-imported="handleBookImport"
      @import-error="handleImportError"
    />
    
    <!-- Reading Interface (New) -->
    <div v-else class="reading-interface">
      <!-- Top Toolbar -->
      <ReadingToolbar
        :book="currentBook"
        :currentPage="currentPage"
        :totalPages="totalPages"
        @navigate="handleNavigation"
        @toggle-toc="showTOC = !showTOC"
        @toggle-bookmarks="showBookmarks = !showBookmarks"
        @toggle-annotations="showAnnotations = !showAnnotations"
        @toggle-settings="showSettings = !showSettings"
        @search="handleSearch"
      />
      
      <!-- Main Content Area -->
      <div class="content-container">
        <!-- Table of Contents Sidebar -->
        <TableOfContents
          v-if="showTOC"
          :chapters="chapters"
          :currentChapter="currentChapter"
          @navigate-to-chapter="navigateToChapter"
        />
        
        <!-- Reading Content -->
        <ReadingContent
          ref="contentArea"
          :content="currentContent"
          :format="bookFormat"
          :annotations="currentAnnotations"
          :searchResults="searchResults"
          @text-selected="handleTextSelection"
          @annotation-clicked="handleAnnotationClick"
          @page-turned="handlePageTurn"
        />
        
        <!-- Annotations Sidebar -->
        <AnnotationsSidebar
          v-if="showAnnotations"
          :annotations="allAnnotations"
          :bookmarks="allBookmarks"
          @navigate-to-annotation="navigateToAnnotation"
          @edit-annotation="editAnnotation"
          @delete-annotation="deleteAnnotation"
        />
      </div>
      
      <!-- Reading Progress Bar -->
      <ReadingProgress
        :current="currentPage"
        :total="totalPages"
        :bookmarks="bookmarkPositions"
        @seek="seekToPage"
      />
    </div>
    
    <!-- Modals -->
    <AnnotationModal
      v-if="showAnnotationModal"
      :selection="currentSelection"
      :existingAnnotation="editingAnnotation"
      @save="saveAnnotation"
      @close="closeAnnotationModal"
    />
    
    <BookmarkModal
      v-if="showBookmarkModal"
      :page="currentPage"
      :chapter="currentChapter"
      @save="saveBookmark"
      @close="showBookmarkModal = false"
    />
    
    <ReadingSettingsModal
      v-if="showSettings"
      :settings="readingSettings"
      @update="updateReadingSettings"
      @close="showSettings = false"
    />
    
    <SearchModal
      v-if="showSearch"
      :bookId="currentBook.id"
      @search-results="handleSearchResults"
      @close="showSearch = false"
    />
  </div>
</template>
```

### 2. New Components

#### 2.1 BookImporter Component

**Purpose**: Enhanced book import with format detection and processing feedback

**Features**:
- Drag & drop support
- Format validation
- Import progress tracking
- Duplicate detection
- Metadata preview

#### 2.2 ReadingToolbar Component

**Purpose**: Main navigation and action toolbar

**Features**:
- Page navigation (with keyboard shortcuts)
- Chapter navigation
- Zoom controls
- Search button
- Bookmark quick-add
- View mode toggle (single page, double page, continuous scroll)
- Reading settings

#### 2.3 ReadingContent Component

**Purpose**: Main content display area

**Key Features**:
- Render different formats (PDF, EPUB, etc.)
- Text selection handling
- Annotation overlay rendering
- Smooth page transitions
- Pinch-to-zoom support
- Responsive layout

**Technical Implementation**:
```typescript
interface ReadingContentProps {
  content: ChapterContent | PageContent
  format: BookFormat
  annotations: Annotation[]
  searchResults: SearchResult[]
}

// Rendering strategies by format
const renderStrategies = {
  pdf: PDFRenderer,
  epub: EPUBRenderer,
  mobi: MOBIRenderer,
  // ... other formats
}
```

#### 2.4 TableOfContents Component

**Purpose**: Navigable chapter/section list

**Features**:
- Hierarchical chapter display
- Current position indicator
- Collapsible sections
- Quick jump navigation
- Progress indicators per chapter

#### 2.5 AnnotationsSidebar Component

**Purpose**: Manage all annotations and bookmarks

**Features**:
- Filter by type (highlight, note, bookmark)
- Sort by position, date, or color
- Quick preview on hover
- Batch operations
- Export annotations

#### 2.6 AnnotationModal Component

**Purpose**: Create/edit annotations

**Features**:
- Rich text note editor
- Color picker for highlights
- Type selector (highlight, underline, note)
- Link to create full note
- Tags/categories

#### 2.7 ReadingProgress Component

**Purpose**: Visual progress indicator with navigation

**Features**:
- Progress bar with chapters marked
- Bookmark indicators
- Click to navigate
- Time remaining estimate
- Reading speed display

### 3. Integration with Existing Components

#### 3.1 NoteEditor Integration

When creating a note from an annotation:

```typescript
// In AnnotationModal
const createFullNote = async () => {
  const note = await createNoteForBook(bookId, {
    title: `Note on "${selectedText.substring(0, 50)}..."`,
    content: annotationContent,
    book_id: bookId,
    annotation_id: annotationId
  })
  
  // Navigate to note editor
  router.push({ name: 'NoteEditor', params: { noteId: note.id } })
}
```

#### 3.2 Media System Integration

For storing extracted images and page renders:

```typescript
// In book import process
const processBookImages = async (bookId: number, images: ExtractedImage[]) => {
  for (const image of images) {
    await saveMediaFile(
      null, // no note_id
      image.data,
      image.filename,
      image.mimeType,
      bookId,
      false // not a cover
    )
  }
}
```

### 4. State Management

#### 4.1 BookReaderStore (Pinia)

```typescript
export const useBookReaderStore = defineStore('bookReader', {
  state: () => ({
    // Current book data
    currentBook: null as Book | null,
    bookContent: null as BookContent | null,
    
    // Reading position
    currentPage: 1,
    currentChapter: null as Chapter | null,
    readingPosition: {} as ReadingPosition,
    
    // UI state
    viewMode: 'single' as ViewMode,
    zoom: 100,
    showTOC: false,
    showAnnotations: false,
    
    // User data
    annotations: [] as Annotation[],
    bookmarks: [] as Bookmark[],
    readingSessions: [] as ReadingSession[],
    readingSettings: {} as ReadingSettings,
  }),
  
  actions: {
    async loadBook(bookId: number) {
      // Load book content from database
    },
    
    async saveReadingProgress() {
      // Update reading position in database
    },
    
    async createAnnotation(annotation: AnnotationData) {
      // Save to database and update state
    }
  }
})
```

### 5. User Interactions

#### 5.1 Text Selection Flow

1. User selects text in ReadingContent
2. Floating toolbar appears with options:
   - Highlight (with color picker)
   - Add Note
   - Copy
   - Create Bookmark
3. Selection saved with precise position data
4. Visual feedback (highlight appears immediately)

#### 5.2 Navigation Patterns

- **Swipe/Arrow Keys**: Next/previous page
- **Space/Shift+Space**: Page down/up
- **Home/End**: Beginning/end of book
- **Ctrl+G**: Go to page
- **Ctrl+F**: Search in book
- **Ctrl+B**: Add bookmark

#### 5.3 Annotation Interaction

- **Click annotation**: Show/edit in modal
- **Hover annotation**: Preview tooltip
- **Right-click**: Context menu (edit, delete, change color)
- **Drag**: Reorder in sidebar

### 6. Responsive Design

#### 6.1 Desktop Layout
- Three-column layout: TOC | Content | Annotations
- Collapsible sidebars
- Full toolbar

#### 6.2 Tablet Layout
- Two-column max: Content + one sidebar
- Simplified toolbar
- Touch gestures for navigation

#### 6.3 Mobile Layout
- Single column
- Bottom navigation bar
- Swipe gestures
- Minimal UI in reading mode

### 7. Performance Considerations

#### 7.1 Lazy Loading
```typescript
// Load pages on demand
const loadPage = async (pageNumber: number) => {
  // Check cache first
  if (pageCache.has(pageNumber)) {
    return pageCache.get(pageNumber)
  }
  
  // Load from database
  const page = await window.db.books.getPage(bookId, pageNumber)
  
  // Cache nearby pages
  preloadPages(pageNumber - 2, pageNumber + 2)
  
  return page
}
```

#### 7.2 Virtual Scrolling
For continuous scroll mode, implement virtual scrolling to handle large books efficiently.

#### 7.3 Image Optimization
- Generate thumbnails for page images
- Progressive loading for high-res PDFs
- Cache rendered pages

### 8. Accessibility

- **Keyboard Navigation**: Full keyboard support
- **Screen Reader**: ARIA labels and live regions
- **High Contrast**: Support theme customization
- **Font Scaling**: Adjustable text size
- **Focus Management**: Proper focus handling in modals

### 9. Integration Points

#### 9.1 With Discord Integration
```typescript
// Update Discord presence while reading
useDiscordActivity().updateActivity({
  state: `Reading: ${book.title}`,
  details: `Page ${currentPage} of ${totalPages}`,
  largeImageKey: 'book-icon',
  smallImageKey: 'reading'
})
```

#### 9.2 With Timer System
```typescript
// Track reading time in timer
const readingTimer = useTimerStore()
readingTimer.startSession({
  type: 'reading',
  category: 'Book Reading',
  focus: book.title
})
```

### 10. Error Handling

- **Import Failures**: Clear error messages with retry options
- **Rendering Issues**: Fallback renderers
- **Sync Conflicts**: Manual resolution UI
- **Corrupt Files**: Graceful degradation

## Development Phases

### Phase 1: Core Reading
1. Basic import functionality
2. PDF rendering
3. Page navigation
4. Reading progress tracking

### Phase 2: Annotations
1. Text selection
2. Highlight creation
3. Note attachments
4. Bookmark system

### Phase 3: Advanced Features
1. Search functionality
2. Table of contents
3. Multiple format support
4. Export annotations

### Phase 4: Polish
1. Animations and transitions
2. Gesture support
3. Performance optimization
4. Accessibility improvements
