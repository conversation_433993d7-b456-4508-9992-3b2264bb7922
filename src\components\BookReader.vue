<template>
  <div class="read-container">
    <!-- Book Import Section (shown when no book file is imported) -->
    <div v-if="!hasBookFile" class="import-section">
      <div class="import-area" @drop="handleFileDrop" @dragover.prevent @dragenter.prevent>
        <div class="import-icon">
          <img src="/icons/book-icon.svg" alt="Book" />
        </div>
        <h4>Import Book File</h4>
        <p>Drag and drop or click to select</p>
        <button class="import-button" @click="triggerFileInput">
          Choose File
        </button>
        <div class="supported-formats">
          Supports: EPUB, PDF, MOBI, AZW3, FB2, CBZ
        </div>
      </div>
      <input ref="bookFileInput" type="file"
             accept=".epub,.pdf,.mobi,.azw3,.fb2,.cbz"
             @change="handleBookImport" style="display: none;">
    </div>

    <!-- Book Reader Section (shown when book file is imported) -->
    <div v-else class="book-reader-container">
      <!-- Reading Controls Header -->
      <div class="reading-controls-header">
        <div class="navigation-controls">
          <button class="nav-control nav-control--prev" @click="previousPage" :disabled="currentPage <= 1" :class="{ 'nav-control--disabled': currentPage <= 1 }">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M10 12L6 8L10 4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
          <div class="page-edit">
            <input
              ref="pageInput"
              type="number"
              :value="currentPage"
              @input="handlePageInput"
              @blur="handlePageEdit"
              @keyup.enter="handlePageEdit"
              :min="1"
              :max="totalPages"
              class="page-input"
            />
            <span class="page-total">/ {{ totalPages }}</span>
          </div>
          <button class="nav-control nav-control--next" @click="nextPage" :disabled="currentPage >= totalPages" :class="{ 'nav-control--disabled': currentPage >= totalPages }">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M6 4L10 8L6 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>
        <div class="reader-actions">
          <button class="reader-action-button" @click="toggleBookmarks" title="Bookmarks">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M3 2a1 1 0 0 1 1-1h8a1 1 0 0 1 1 1v12l-5-3-5 3V2z" fill="currentColor"/>
            </svg>
          </button>
          <button class="reader-action-button" @click="toggleSettings" title="Settings">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z" fill="currentColor"/>
              <path d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319zm-2.633.283c.246-.835 1.428-.835 1.674 0l.094.319a1.873 1.873 0 0 0 2.693 1.115l.291-.16c.764-.415 1.6.42 1.184 1.185l-.159.292a1.873 1.873 0 0 0 1.116 2.692l.318.094c.835.246.835 1.428 0 1.674l-.319.094a1.873 1.873 0 0 0-1.115 2.693l.16.291c.415.764-.42 1.6-1.185 1.184l-.291-.159a1.873 1.873 0 0 0-2.693 1.116l-.094.318c-.246.835-1.428.835-1.674 0l-.094-.319a1.873 1.873 0 0 0-2.692-1.115l-.292.16c-.764.415-1.6-.42-1.184-1.185l.159-.291A1.873 1.873 0 0 0 1.945 8.93l-.319-.094c-.835-.246-.835-1.428 0-1.674l.319-.094A1.873 1.873 0 0 0 3.06 4.377l-.16-.292c-.415-.764.42-1.6 1.185-1.184l.292.159a1.873 1.873 0 0 0 2.692-1.115l.094-.319z" fill="currentColor"/>
            </svg>
          </button>

        </div>
      </div>

      <!-- Main Reading Area -->
      <div class="reading-area" ref="readingArea">
        <div class="book-content">
          <!-- Loading state -->
          <div v-if="isLoadingContent" class="loading-content">
            <div class="loading-spinner"></div>
            <p>Loading page...</p>
          </div>
          
          <!-- Error state -->
          <div v-else-if="contentError" class="error-content">
            <p class="error-message">{{ contentError }}</p>
          </div>
          
          <!-- Book content -->
          <div v-else-if="currentPageContent" class="page-content" v-html="currentPageContent"></div>
          
          <!-- Placeholder when no content -->
          <div v-else class="content-placeholder">
            <p class="no-content-message">No content available for this page.</p>
          </div>
        </div>
      </div>

      <!-- Reading Progress Bar -->
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, ref, onMounted, computed, watch, onUnmounted } from 'vue'
import type { BookWithNoteCount } from '../types/electron-api'

export default defineComponent({
  name: 'BookReader',
  props: {
    book: {
      type: Object as PropType<BookWithNoteCount>,
      required: true
    },

  },
  setup(props) {
    // Read tab reactive variables
    const hasBookFile = ref(false)
    const currentPage = ref(1)
    const totalPages = ref(100)
    const progressPercentage = ref(0)
    const bookFileInput = ref<HTMLInputElement | null>(null)
    const pageInput = ref<HTMLInputElement | null>(null)
    const readingArea = ref<HTMLElement | null>(null)
    
    // Book content state
    const bookContent = ref<any>(null)
    const chapters = ref<any[]>([])
    const currentPageContent = ref<string>('')
    const isLoadingContent = ref(false)
    const contentError = ref<string>('')
    const readingSessionId = ref<number | null>(null)

    // Read tab methods

    const triggerFileInput = () => {
      bookFileInput.value?.click();
    };

    const handleFileDrop = (event: DragEvent) => {
      event.preventDefault();
      const files = event.dataTransfer?.files;
      if (files && files.length > 0) {
        processBookFile(files[0]);
      }
    };

    const handleBookImport = (event: Event) => {
      const file = (event.target as HTMLInputElement).files?.[0];
      if (file) {
        processBookFile(file);
      }
    };

    const processBookFile = async (file: File) => {
      try {
        isLoadingContent.value = true;
        contentError.value = '';
        
        // Note: In a real implementation, we would need to handle file upload differently
        // since browsers don't provide file paths for security reasons.
        // This would typically involve:
        // 1. Using a file dialog in the main process
        // 2. Or uploading the file to a temporary location first
        alert('Book file import requires file dialog implementation in main process.');
        
        // For now, show a message to the user
        contentError.value = 'Book import requires using the "Import Book" button in the Books view.';
        
        // TODO: Implement proper file handling through Electron's dialog API
        // const result = await window.electronAPI.importBookFile(props.book.id!, file);
        
      } catch (error) {
        console.error('Failed to import book file:', error);
        contentError.value = 'Failed to import book file. Please try again.';
      } finally {
        isLoadingContent.value = false;
      }
    };

    const loadPageContent = async () => {
      if (!hasBookFile.value || !props.book.id) return;
      
      try {
        isLoadingContent.value = true;
        const pageData = await window.db.bookContent.getPage({
          bookId: props.book.id,
          pageNumber: currentPage.value
        });
        
        currentPageContent.value = pageData.content || '';
        updateProgress();
      } catch (error) {
        console.error('Failed to load page content:', error);
        currentPageContent.value = 'Failed to load page content.';
      } finally {
        isLoadingContent.value = false;
      }
    };
    
    const previousPage = async () => {
      if (currentPage.value > 1) {
        currentPage.value--;
        await loadPageContent();
      }
    };

    const nextPage = async () => {
      if (currentPage.value < totalPages.value) {
        currentPage.value++;
        await loadPageContent();
      }
    };

    const updateProgress = async () => {
      progressPercentage.value = (currentPage.value / totalPages.value) * 100;
      
      // Update reading progress in database
      if (props.book.id && hasBookFile.value) {
        await window.db.bookContent.updateProgress({
          bookId: props.book.id,
          currentPage: currentPage.value,
          percentage: progressPercentage.value
        });
      }
    };

    const toggleBookmarks = () => {
      console.log('Toggle bookmarks');
      // Placeholder for bookmarks functionality
    };

    const toggleSettings = () => {
      console.log('Toggle settings');
      // Placeholder for settings functionality
    };

    const handlePageInput = (event: Event) => {
      const input = event.target as HTMLInputElement;
      const newPage = parseInt(input.value);

      if (newPage >= 1 && newPage <= totalPages.value) {
        currentPage.value = newPage;
        updateProgress();
      }
    };

    const handlePageEdit = async (event: Event) => {
      const input = event.target as HTMLInputElement;
      const newPage = parseInt(input.value);

      if (newPage >= 1 && newPage <= totalPages.value) {
        currentPage.value = newPage;
        await loadPageContent();
      } else {
        // Reset to current page if invalid
        input.value = currentPage.value.toString();
      }
    };
    
    // Check if book already has content on mount
    onMounted(async () => {
      if (props.book.id) {
        const hasContent = await window.db.bookContent.hasContent(props.book.id);
        if (hasContent) {
          hasBookFile.value = true;
          
          // Load book metadata
          bookContent.value = await window.db.bookContent.getMetadata(props.book.id);
          chapters.value = await window.db.bookContent.getChapters(props.book.id);
          
          if (bookContent.value) {
            totalPages.value = bookContent.value.totalPages || 100;
            currentPage.value = bookContent.value.currentPage || 1;
            await loadPageContent();
            
            // Start reading session
            const session = await window.db.bookContent.startSession({
              bookId: props.book.id,
              startPage: currentPage.value
            });
            readingSessionId.value = session.sessionId;
          }
        }
      }
    });
    
    // End reading session on unmount
    onUnmounted(async () => {
      if (readingSessionId.value && currentPage.value) {
        await window.db.bookContent.endSession(readingSessionId.value, currentPage.value);
      }
    });

    return {
      // Reactive variables
      hasBookFile,
      currentPage,
      totalPages,
      progressPercentage,
      bookFileInput,
      pageInput,
      readingArea,
      currentPageContent,
      isLoadingContent,
      contentError,
      // Methods
      triggerFileInput,
      handleFileDrop,
      handleBookImport,
      previousPage,
      nextPage,
      toggleBookmarks,
      toggleSettings,
      handlePageInput,
      handlePageEdit
    }
  }
})
</script>

<style scoped>
/* Read Container Styles */
.read-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  transition: all 0.3s ease-in-out;
}

/* Import Section Styles */
.import-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.import-area {
  text-align: center;
  padding: 40px;
  border: 2px dashed var(--color-border-primary);
  border-radius: 12px;
  background-color: var(--color-bg-secondary);
  transition: all 0.2s;
  cursor: pointer;
  max-width: 400px;
  width: 100%;
}

.import-area:hover {
  border-color: var(--color-primary);
  background-color: var(--color-bg-tertiary);
}

.import-icon {
  margin-bottom: 16px;
}

.import-icon img {
  width: 48px;
  height: 48px;
  opacity: 0.7;
}

.import-area h4 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.import-area p {
  margin: 0 0 20px 0;
  color: var(--color-text-secondary);
  font-size: 14px;
}

.import-button {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: 1px solid var(--color-btn-secondary-border);
  border-radius: 8px;
  padding: 12px 24px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 16px;
}

.import-button:hover {
  background-color: var(--color-btn-secondary-hover);
}

.supported-formats {
  font-size: 12px;
  color: var(--color-text-tertiary);
  margin-top: 12px;
}

/* Book Reader Styles */
.book-reader-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.reading-controls-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border-primary);
  background-color: var(--color-bg-secondary);
  flex-shrink: 0;
}

.navigation-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-control {
  width: 32px;
  height: 32px;
  background: var(--color-btn-secondary-bg);
  border: 1px solid var(--color-btn-secondary-border);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--color-text-secondary);
  transition: all 0.2s ease;
}

.nav-control:hover:not(.nav-control--disabled) {
  background: var(--color-btn-secondary-hover);
  color: var(--color-text-primary);
  border-color: var(--color-border-hover);
}

.nav-control:active:not(.nav-control--disabled) {
  transform: scale(0.95);
}

.nav-control--disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.page-edit {
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 80px;
  justify-content: center;
}

.page-input {
  width: 50px;
  padding: 4px 6px;
  border: 1px solid var(--color-border-primary);
  border-radius: 4px;
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  font-family: inherit;
  transition: border-color 0.2s;
}

.page-input:focus {
  outline: none;
  border-color: var(--color-primary);
}

.page-total {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-primary);
}

.reader-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reader-action-button {
  width: 32px;
  height: 32px;
  background-color: var(--color-primary);
  border: 1px solid var(--color-primary);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-inverse);
}

.reader-action-button:hover {
  background-color: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

.reader-action-button:active {
  transform: scale(0.95);
}

.reader-action-button svg {
  width: 16px;
  height: 16px;
}

/* Reading Area Styles */
.reading-area {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: var(--color-bg-primary);
  min-height: 0;
}

.reading-area::-webkit-scrollbar {
  width: 8px;
}

.reading-area::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
}

.reading-area::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.reading-area::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

.book-content {
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

.content-placeholder h3 {
  font-size: 24px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0 0 20px 0;
  text-align: center;
}

.book-content-text {
  font-size: 16px;
  color: var(--color-text-primary);
  margin: 0 0 16px 0;
  text-align: justify;
  font-family: 'Georgia', serif;
}

/* Loading and Error States */
.loading-content,
.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  gap: 16px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-border-primary);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  color: var(--color-error);
  font-size: 16px;
  margin: 0;
}

.no-content-message {
  color: var(--color-text-secondary);
  font-size: 16px;
  text-align: center;
  margin: 40px 0;
}

/* Page Content Styles */
.page-content {
  font-size: 16px;
  line-height: 1.8;
  color: var(--color-text-primary);
  font-family: 'Georgia', serif;
}

.page-content p {
  margin: 0 0 1.5em 0;
  text-align: justify;
}

.page-content h1,
.page-content h2,
.page-content h3,
.page-content h4,
.page-content h5,
.page-content h6 {
  font-family: 'Montserrat', sans-serif;
  margin: 1.5em 0 0.5em 0;
  color: var(--color-text-primary);
}

.page-content h1 {
  font-size: 2em;
}

.page-content h2 {
  font-size: 1.5em;
}

.page-content h3 {
  font-size: 1.25em;
}

.page-content img {
  max-width: 100%;
  height: auto;
  margin: 1em 0;
}

.page-content blockquote {
  margin: 1em 0;
  padding-left: 1.5em;
  border-left: 4px solid var(--color-border-primary);
  font-style: italic;
  color: var(--color-text-secondary);
}

.page-content code {
  background-color: var(--color-bg-secondary);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
  font-size: 0.9em;
}

.page-content pre {
  background-color: var(--color-bg-secondary);
  padding: 1em;
  border-radius: 6px;
  overflow-x: auto;
  margin: 1em 0;
}

.page-content pre code {
  background-color: transparent;
  padding: 0;
}

/* Progress Bar Styles */
.progress-bar {
  height: 4px;
  background-color: var(--color-bg-secondary);
  border-radius: 2px;
  overflow: hidden;
  margin: 0 16px 16px 16px;
  flex-shrink: 0;
}

.progress-fill {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .reading-controls-header {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }

  .navigation-controls {
    gap: 8px;
  }

  .page-input {
    width: 40px;
    font-size: 12px;
  }

  .page-total {
    font-size: 12px;
  }

  .reader-action-button {
    width: 28px;
    height: 28px;
  }

  .reader-action-button svg {
    width: 14px;
    height: 14px;
  }

  .book-content-text {
    font-size: 18px;
  }
}

@media (max-width: 640px) {
  .reading-controls-header {
    padding: 12px;
  }

  .navigation-controls {
    gap: 8px;
  }

  .page-input {
    width: 35px;
    font-size: 11px;
  }

  .page-total {
    font-size: 11px;
  }

  .reader-action-button {
    width: 26px;
    height: 26px;
  }

  .reader-action-button svg {
    width: 12px;
    height: 12px;
  }

  .page-total {
    font-size: 12px;
  }

  .reading-area {
    padding: 16px;
  }

  .book-content-text {
    font-size: 14px;
  }
}
</style>
