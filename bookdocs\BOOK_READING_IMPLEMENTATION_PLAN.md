# Book Reading System - Complete Implementation Plan

## Table of Contents
1. [Overview](#overview)
2. [Architecture Overview](#architecture-overview)
3. [Implementation Phases](#implementation-phases)
4. [Phase 1: Database Schema Extensions](#phase-1-database-schema-extensions)
5. [Phase 2: Foliate.js Integration](#phase-2-foliatejs-integration)
6. [Phase 3: API Layer Implementation](#phase-3-api-layer-implementation)
7. [Phase 4: BookReader Component Enhancement](#phase-4-bookreader-component-enhancement)
8. [Phase 5: New Component Creation](#phase-5-new-component-creation)
9. [Phase 6: State Management](#phase-6-state-management)
10. [Phase 7: Annotation System](#phase-7-annotation-system)
11. [Phase 8: Sync Integration](#phase-8-sync-integration)
12. [Phase 9: Testing and Polish](#phase-9-testing-and-polish)
13. [Technical Specifications](#technical-specifications)
14. [Risk Mitigation](#risk-mitigation)
15. [Success Metrics](#success-metrics)

## Overview

This document outlines the complete implementation plan for adding comprehensive book reading functionality to Noti. The system will transform Noti from a note-taking application into a complete digital reading and knowledge management platform.

### Key Features
- **Multi-format Support**: PDF, EPUB, MOBI, AZW3, FB2, CBZ
- **Rich Annotations**: Highlights, bookmarks, notes with seamless integration
- **Full-text Search**: Search within books using SQLite FTS5
- **Reading Progress**: Track and sync reading position across devices
- **Note Integration**: Convert any annotation into a full Noti note

### Implementation Philosophy
- **Incremental Development**: Each phase can be tested independently
- **Reversible Changes**: All database migrations can be rolled back
- **Simple Solutions**: Leverage existing patterns and infrastructure
- **No Breaking Changes**: Preserve all existing functionality

## Architecture Overview

### User Flow
1. User clicks "Read" button in `BookDetailsModal`
2. `BookDetailsModal` emits `open-reader` event
3. Parent component opens `BookReader.vue` (potentially in a new route or modal)
4. `BookReader` loads book content and displays reading interface
5. User can annotate, bookmark, and convert selections to notes

### Component Architecture
```
BookDetailsModal.vue (existing)
├── Details Tab
├── Notes Tab
└── Read Button → opens BookReader.vue

BookReader.vue (enhanced)
├── BookImporter (file upload)
├── ReadingContent (Foliate.js integration)
├── ReadingToolbar (navigation, settings)
├── AnnotationToolbar (text selection actions)
└── Sub-components
    ├── ReaderSettingsModal
    ├── BookmarksPanel
    ├── AnnotationSidebar
    ├── TableOfContents
    └── ReadingProgress
```

### Data Flow
```
Frontend (Vue) → IPC Handlers → API Layer → Database
                                    ↓
                              File System (book files)
```

## Implementation Phases

### Phase Overview
1. **Database Schema** (High Priority) - Foundation for all book data
2. **Foliate.js Integration** (High Priority) - Core parsing capability
3. **API Layer** (High Priority) - Backend infrastructure
4. **BookReader Enhancement** (High Priority) - Core UI functionality
5. **New Components** (Medium Priority) - Enhanced UI features
6. **State Management** (Medium Priority) - Client-side state
7. **Annotation System** (Medium Priority) - Reading features
8. **Sync Integration** (Low Priority) - Cross-device support
9. **Testing & Polish** (Low Priority) - Quality assurance

## Phase 1: Database Schema Extensions

### 1.1 Migration Script Creation
Create `electron/main/database/migrations/001_book_reading_schema.ts`:

```typescript
export const up = async (db: Database) => {
  // Add columns to existing books table
  await db.run(`
    ALTER TABLE books ADD COLUMN has_content BOOLEAN DEFAULT 0;
    ALTER TABLE books ADD COLUMN last_read_at TIMESTAMP;
    ALTER TABLE books ADD COLUMN reading_position TEXT;
    ALTER TABLE books ADD COLUMN reading_percentage REAL DEFAULT 0;
    ALTER TABLE books ADD COLUMN total_reading_time INTEGER DEFAULT 0;
    ALTER TABLE books ADD COLUMN bookmark_count INTEGER DEFAULT 0;
    ALTER TABLE books ADD COLUMN annotation_count INTEGER DEFAULT 0;
    ALTER TABLE books ADD COLUMN book_file_path TEXT;
    ALTER TABLE books ADD COLUMN book_file_format TEXT;
    ALTER TABLE books ADD COLUMN file_size INTEGER;
  `);

  // Create new tables...
};

export const down = async (db: Database) => {
  // Rollback logic
};
```

### 1.2 New Tables

#### book_content Table
```sql
CREATE TABLE IF NOT EXISTS book_content (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    format TEXT NOT NULL CHECK (format IN ('pdf', 'epub', 'mobi', 'azw3', 'fb2', 'cbz')),
    original_file BLOB,
    file_hash TEXT NOT NULL,
    total_pages INTEGER,
    total_chapters INTEGER,
    table_of_contents TEXT,
    metadata TEXT,
    text_content TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
    UNIQUE(book_id)
);
```

#### book_chapters Table
```sql
CREATE TABLE IF NOT EXISTS book_chapters (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    chapter_number INTEGER NOT NULL,
    title TEXT,
    start_page INTEGER,
    end_page INTEGER,
    content_html TEXT,
    content_text TEXT,
    "order" INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
    UNIQUE(book_id, chapter_number)
);
```

#### book_pages Table
```sql
CREATE TABLE IF NOT EXISTS book_pages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    chapter_id INTEGER,
    page_number INTEGER NOT NULL,
    content_html TEXT,
    content_text TEXT,
    page_image_id INTEGER,
    width INTEGER,
    height INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
    FOREIGN KEY (chapter_id) REFERENCES book_chapters(id) ON DELETE SET NULL,
    FOREIGN KEY (page_image_id) REFERENCES media_files(id) ON DELETE SET NULL,
    UNIQUE(book_id, page_number)
);
```

#### book_bookmarks Table
```sql
CREATE TABLE IF NOT EXISTS book_bookmarks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    user_id INTEGER,
    name TEXT,
    description TEXT,
    page_number INTEGER,
    chapter_id INTEGER,
    position_data TEXT,
    color TEXT,
    "order" INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
    FOREIGN KEY (chapter_id) REFERENCES book_chapters(id) ON DELETE SET NULL
);
```

#### book_annotations Table
```sql
CREATE TABLE IF NOT EXISTS book_annotations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    note_id INTEGER,
    user_id INTEGER,
    type TEXT NOT NULL CHECK (type IN ('highlight', 'note', 'underline', 'comment')),
    content TEXT,
    selected_text TEXT,
    color TEXT,
    page_number INTEGER,
    chapter_id INTEGER,
    start_position TEXT,
    end_position TEXT,
    pdf_coords TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
    FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE SET NULL,
    FOREIGN KEY (chapter_id) REFERENCES book_chapters(id) ON DELETE SET NULL
);
```

#### book_reading_sessions Table
```sql
CREATE TABLE IF NOT EXISTS book_reading_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    user_id INTEGER,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    start_page INTEGER,
    end_page INTEGER,
    pages_read INTEGER,
    duration_seconds INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);
```

#### book_reading_settings Table
```sql
CREATE TABLE IF NOT EXISTS book_reading_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    font_size INTEGER DEFAULT 16,
    font_family TEXT DEFAULT 'Georgia',
    line_height REAL DEFAULT 1.6,
    theme TEXT DEFAULT 'light',
    page_width INTEGER DEFAULT 800,
    margin INTEGER DEFAULT 20,
    flow TEXT DEFAULT 'paginated',
    max_column_count INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
    UNIQUE(book_id)
);
```

#### book_search_index Virtual Table
```sql
CREATE VIRTUAL TABLE IF NOT EXISTS book_search_index USING fts5(
    book_id,
    page_number,
    chapter_id,
    content,
    tokenize='porter unicode61'
);
```

### 1.3 Update notes Table
```sql
ALTER TABLE notes ADD COLUMN annotation_id INTEGER REFERENCES book_annotations(id) ON DELETE SET NULL;
ALTER TABLE notes ADD COLUMN reading_context TEXT;
```

### 1.4 Integration with database.ts
Update `electron/main/database/database.ts` to include new table creation in `createAllTables()` function.

## Phase 2: Foliate.js Integration

### 2.1 Install Dependencies
```bash
npm install foliate-js pdfjs-dist jszip epub.js
npm install --save-dev @types/pdfjs-dist
```

### 2.2 Create Book Parser Module
Create `electron/main/lib/book-parser.ts`:

```typescript
import { Foliate } from 'foliate-js';
import * as PDFJS from 'pdfjs-dist';
import * as path from 'node:path';
import * as fs from 'node:fs/promises';
import crypto from 'crypto';

// Configure PDF.js worker
PDFJS.GlobalWorkerOptions.workerSrc = 
  path.join(__dirname, '../node_modules/pdfjs-dist/build/pdf.worker.js');

export interface ParsedBook {
  metadata: BookMetadata;
  format: BookFormat;
  fileHash: string;
  chapters?: Chapter[];
  pages?: Page[];
  toc: TableOfContentsItem[];
  totalPages: number;
  originalFile: Buffer;
}

export class BookParser {
  private foliate: Foliate;
  
  constructor() {
    this.foliate = new Foliate({
      formats: ['epub', 'pdf', 'mobi', 'azw3', 'fb2', 'cbz'],
      renderOptions: {
        width: 800,
        height: 600,
        scale: 1.5
      }
    });
  }
  
  async parseBook(filePath: string): Promise<ParsedBook> {
    const fileBuffer = await fs.readFile(filePath);
    const fileHash = crypto.createHash('sha256').update(fileBuffer).digest('hex');
    
    const format = await this.detectFormat(filePath, fileBuffer);
    
    switch (format) {
      case 'epub':
        return await this.parseEPUB(fileBuffer, fileHash);
      case 'pdf':
        return await this.parsePDF(fileBuffer, fileHash);
      default:
        throw new Error(`Unsupported format: ${format}`);
    }
  }
  
  private async parseEPUB(buffer: Buffer, fileHash: string): Promise<ParsedBook> {
    // Implementation details...
  }
  
  private async parsePDF(buffer: Buffer, fileHash: string): Promise<ParsedBook> {
    // Implementation details...
  }
}
```

### 2.3 Content Storage Manager
Create `electron/main/api/book-content-manager.ts`:

```typescript
export class BookContentManager {
  async importBook(filePath: string, bookId: number): Promise<void> {
    const parser = new BookParser();
    const parsed = await parser.parseBook(filePath);
    
    await db.transaction(async (trx) => {
      // Store main content
      await this.storeBookContent(trx, bookId, parsed);
      
      // Store chapters/pages
      if (parsed.format === 'epub') {
        await this.storeChapters(trx, bookId, parsed.chapters);
      } else if (parsed.format === 'pdf') {
        await this.storePages(trx, bookId, parsed.pages);
      }
      
      // Update book record
      await this.updateBookRecord(trx, bookId, {
        has_content: true,
        page_count: parsed.totalPages,
        book_file_format: parsed.format
      });
    });
    
    // Build search index in background
    this.buildSearchIndex(bookId, parsed);
  }
}
```

## Phase 3: API Layer Implementation

### 3.1 Book Content API
Create `electron/main/api/book-content-api.ts`:

```typescript
import { BookContentManager } from './book-content-manager';
import { BookRenderer } from '../lib/book-renderer';

export const bookContentApi = {
  async importBookFile(bookId: number, filePath: string): Promise<void> {
    const manager = new BookContentManager();
    return await manager.importBook(filePath, bookId);
  },
  
  async getChapter(bookId: number, chapterId: number): Promise<Chapter> {
    return await db.get('SELECT * FROM book_chapters WHERE book_id = ? AND id = ?', 
      [bookId, chapterId]);
  },
  
  async getPage(bookId: number, pageNumber: number): Promise<Page> {
    return await db.get('SELECT * FROM book_pages WHERE book_id = ? AND page_number = ?',
      [bookId, pageNumber]);
  },
  
  async getTableOfContents(bookId: number): Promise<TableOfContentsItem[]> {
    const content = await db.get('SELECT table_of_contents FROM book_content WHERE book_id = ?', [bookId]);
    return JSON.parse(content.table_of_contents || '[]');
  }
};
```

### 3.2 Annotation API
Create `electron/main/api/annotation-api.ts`:

```typescript
export const annotationApi = {
  async createAnnotation(data: CreateAnnotationData): Promise<Annotation> {
    const result = await db.run(`
      INSERT INTO book_annotations (
        book_id, type, content, selected_text, color, 
        page_number, chapter_id, start_position, end_position
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [data.book_id, data.type, data.content, data.selected_text, 
       data.color, data.page_number, data.chapter_id,
       JSON.stringify(data.start_position), JSON.stringify(data.end_position)]
    );
    
    // Update annotation count
    await this.updateAnnotationCount(data.book_id);
    
    return { id: result.lastID, ...data };
  },
  
  async getAnnotationsForPage(bookId: number, pageNumber: number): Promise<Annotation[]> {
    return await db.all(
      'SELECT * FROM book_annotations WHERE book_id = ? AND page_number = ? ORDER BY start_position',
      [bookId, pageNumber]
    );
  },
  
  async createBookmark(data: CreateBookmarkData): Promise<Bookmark> {
    const result = await db.run(`
      INSERT INTO book_bookmarks (
        book_id, name, description, page_number, chapter_id, position_data, color
      ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [data.book_id, data.name, data.description, data.page_number,
       data.chapter_id, JSON.stringify(data.position_data), data.color]
    );
    
    // Update bookmark count
    await this.updateBookmarkCount(data.book_id);
    
    return { id: result.lastID, ...data };
  },
  
  async convertToNote(annotationId: number): Promise<Note> {
    const annotation = await this.getAnnotationById(annotationId);
    const book = await booksApi.getBookById(annotation.book_id);
    
    const note = await notesApi.createNote({
      title: `Note on "${annotation.selected_text.substring(0, 50)}..."`,
      content: this.generateNoteContent(annotation, book),
      book_id: annotation.book_id,
      annotation_id: annotationId,
      reading_context: JSON.stringify({
        page: annotation.page_number,
        selected_text: annotation.selected_text
      })
    });
    
    // Link annotation to note
    await db.run('UPDATE book_annotations SET note_id = ? WHERE id = ?',
      [note.id, annotationId]);
    
    return note;
  }
};
```

### 3.3 Book Search API
Create `electron/main/api/book-search-api.ts`:

```typescript
export const bookSearchApi = {
  async buildSearchIndex(bookId: number): Promise<void> {
    const chapters = await db.all('SELECT * FROM book_chapters WHERE book_id = ?', [bookId]);
    const pages = await db.all('SELECT * FROM book_pages WHERE book_id = ?', [bookId]);
    
    // Clear existing index
    await db.run('DELETE FROM book_search_index WHERE book_id = ?', [bookId]);
    
    // Index content
    for (const chapter of chapters) {
      await db.run(
        'INSERT INTO book_search_index (book_id, chapter_id, content) VALUES (?, ?, ?)',
        [bookId, chapter.id, chapter.content_text]
      );
    }
    
    for (const page of pages) {
      await db.run(
        'INSERT INTO book_search_index (book_id, page_number, content) VALUES (?, ?, ?)',
        [bookId, page.page_number, page.content_text]
      );
    }
  },
  
  async searchInBook(bookId: number, query: string): Promise<SearchResult[]> {
    return await db.all(`
      SELECT 
        book_id,
        page_number,
        chapter_id,
        snippet(book_search_index, -1, '<mark>', '</mark>', '...', 64) as snippet,
        rank
      FROM book_search_index
      WHERE book_id = ? AND content MATCH ?
      ORDER BY rank
      LIMIT 50`,
      [bookId, query]
    );
  }
};
```

### 3.4 IPC Handler Updates
Add to `electron/main/ipc-handlers.ts`:

```typescript
// Book content operations
ipcMain.handle('books:importFile', async (_event, bookId: number, filePath: string) => {
  try {
    return await bookContentApi.importBookFile(bookId, filePath);
  } catch (error) {
    console.error('IPC books:importFile error:', error);
    throw error;
  }
});

ipcMain.handle('books:getChapter', async (_event, bookId: number, chapterId: number) => {
  try {
    return await bookContentApi.getChapter(bookId, chapterId);
  } catch (error) {
    console.error('IPC books:getChapter error:', error);
    throw error;
  }
});

ipcMain.handle('books:getPage', async (_event, bookId: number, pageNumber: number) => {
  try {
    return await bookContentApi.getPage(bookId, pageNumber);
  } catch (error) {
    console.error('IPC books:getPage error:', error);
    throw error;
  }
});

// Annotation operations
ipcMain.handle('annotations:create', async (_event, data: CreateAnnotationData) => {
  try {
    return await annotationApi.createAnnotation(data);
  } catch (error) {
    console.error('IPC annotations:create error:', error);
    throw error;
  }
});

ipcMain.handle('annotations:getForPage', async (_event, bookId: number, pageNumber: number) => {
  try {
    return await annotationApi.getAnnotationsForPage(bookId, pageNumber);
  } catch (error) {
    console.error('IPC annotations:getForPage error:', error);
    throw error;
  }
});

ipcMain.handle('annotations:convertToNote', async (_event, annotationId: number) => {
  try {
    return await annotationApi.convertToNote(annotationId);
  } catch (error) {
    console.error('IPC annotations:convertToNote error:', error);
    throw error;
  }
});

// Bookmark operations
ipcMain.handle('bookmarks:create', async (_event, data: CreateBookmarkData) => {
  try {
    return await annotationApi.createBookmark(data);
  } catch (error) {
    console.error('IPC bookmarks:create error:', error);
    throw error;
  }
});

// Search operations
ipcMain.handle('books:search', async (_event, bookId: number, query: string) => {
  try {
    return await bookSearchApi.searchInBook(bookId, query);
  } catch (error) {
    console.error('IPC books:search error:', error);
    throw error;
  }
});

// Reading progress
ipcMain.handle('books:updateReadingProgress', async (_event, bookId: number, progress: ReadingProgress) => {
  try {
    return await bookContentApi.updateReadingProgress(bookId, progress);
  } catch (error) {
    console.error('IPC books:updateReadingProgress error:', error);
    throw error;
  }
});

ipcMain.handle('books:getReadingProgress', async (_event, bookId: number) => {
  try {
    return await bookContentApi.getReadingProgress(bookId);
  } catch (error) {
    console.error('IPC books:getReadingProgress error:', error);
    throw error;
  }
});
```

## Phase 4: BookReader Component Enhancement

### 4.1 Update BookReader.vue Structure
Enhance the existing `src/components/BookReader.vue`:

```vue
<template>
  <div class="book-reader" :class="{ 'expanded': isExpanded }">
    <!-- Book Import Section (existing) -->
    <div v-if="!hasBookContent" class="import-section">
      <!-- Keep existing import UI -->
    </div>

    <!-- Enhanced Book Reader Section -->
    <div v-else class="book-reader-container">
      <!-- Enhanced Reading Controls Header -->
      <div class="reading-controls-header">
        <div class="navigation-controls">
          <button @click="previousPage" :disabled="!canGoPrevious">
            <img src="/icons/goback-icon.svg" alt="Previous" />
          </button>
          <div class="page-info">
            <input 
              v-model.number="currentPage" 
              @change="goToPage"
              type="number"
              :min="1"
              :max="totalPages"
            />
            <span>/ {{ totalPages }}</span>
          </div>
          <button @click="nextPage" :disabled="!canGoNext">
            <img src="/icons/goback-icon.svg" alt="Next" style="transform: rotate(180deg)" />
          </button>
        </div>
        
        <div class="reader-actions">
          <button @click="showTableOfContents = !showTableOfContents" title="Table of Contents">
            <img src="/icons/list-bullet-icon.svg" alt="TOC" />
          </button>
          <button @click="showBookmarks = !showBookmarks" title="Bookmarks">
            <img src="/icons/bookmark-icon.svg" alt="Bookmarks" />
          </button>
          <button @click="showAnnotations = !showAnnotations" title="Annotations">
            <img src="/icons/notes-icon.svg" alt="Annotations" />
          </button>
          <button @click="showSearch = !showSearch" title="Search">
            <img src="/icons/search-icon.svg" alt="Search" />
          </button>
          <button @click="showSettings = !showSettings" title="Settings">
            <img src="/icons/settings-icon.svg" alt="Settings" />
          </button>
          <button @click="toggleExpanded" title="Toggle Fullscreen">
            <img :src="isExpanded ? '/icons/minimize-icon.svg' : '/icons/maximize-icon.svg'" alt="Expand" />
          </button>
        </div>
      </div>

      <!-- Main Content Area -->
      <div class="content-container">
        <!-- Table of Contents Sidebar -->
        <TableOfContents
          v-if="showTableOfContents"
          :chapters="tableOfContents"
          :currentChapter="currentChapter"
          @navigate="navigateToChapter"
        />
        
        <!-- Reading Content Area -->
        <div class="reading-area" ref="readingArea">
          <!-- Foliate View Integration -->
          <foliate-view
            v-if="bookFormat === 'epub'"
            ref="foliateView"
            :src="bookContentUrl"
            @relocate="handleLocationChange"
            @load="handleBookLoad"
          />
          
          <!-- PDF View -->
          <div v-else-if="bookFormat === 'pdf'" class="pdf-container">
            <canvas ref="pdfCanvas"></canvas>
            <div class="text-layer" ref="textLayer"></div>
          </div>
          
          <!-- Loading State -->
          <div v-if="isLoading" class="loading-container">
            <div class="loading-spinner"></div>
            <p>Loading book content...</p>
          </div>
          
          <!-- Error State -->
          <div v-if="loadError" class="error-container">
            <p>{{ loadError }}</p>
            <button @click="retryLoad">Retry</button>
          </div>
        </div>
        
        <!-- Annotations Sidebar -->
        <AnnotationsSidebar
          v-if="showAnnotations"
          :annotations="annotations"
          :bookmarks="bookmarks"
          @navigate="navigateToAnnotation"
          @edit="editAnnotation"
          @delete="deleteAnnotation"
          @convert-to-note="convertAnnotationToNote"
        />
      </div>

      <!-- Annotation Toolbar (appears on text selection) -->
      <AnnotationToolbar
        v-if="currentSelection"
        :selection="currentSelection"
        :position="selectionPosition"
        @highlight="createHighlight"
        @note="createNote"
        @bookmark="createBookmark"
      />

      <!-- Reading Progress Bar -->
      <ReadingProgress
        :current="currentPage"
        :total="totalPages"
        :percentage="readingPercentage"
        :bookmarks="bookmarkPositions"
        @seek="seekToPosition"
      />
    </div>

    <!-- Modals -->
    <ReaderSettingsModal
      v-if="showSettings"
      :settings="readingSettings"
      @update="updateReadingSettings"
      @close="showSettings = false"
    />
    
    <BookmarkModal
      v-if="showBookmarkModal"
      :page="currentPage"
      :chapter="currentChapter"
      @save="saveBookmark"
      @close="showBookmarkModal = false"
    />
    
    <SearchModal
      v-if="showSearch"
      :bookId="book.id"
      @results="handleSearchResults"
      @navigate="navigateToSearchResult"
      @close="showSearch = false"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useBookReaderStore } from '@/stores/bookReaderStore'
import { useTextSelection } from '@/composables/useTextSelection'
import { useReadingProgress } from '@/composables/useReadingProgress'
import TableOfContents from './reader/TableOfContents.vue'
import AnnotationsSidebar from './reader/AnnotationsSidebar.vue'
import AnnotationToolbar from './reader/AnnotationToolbar.vue'
import ReadingProgress from './reader/ReadingProgress.vue'
import ReaderSettingsModal from './modals/ReaderSettingsModal.vue'
import BookmarkModal from './modals/BookmarkModal.vue'
import SearchModal from './modals/SearchModal.vue'

const props = defineProps<{
  book: BookWithNoteCount
}>()

const bookReaderStore = useBookReaderStore()
const { selection: currentSelection, clearSelection } = useTextSelection()
const { saveProgress, loadProgress } = useReadingProgress(props.book.id)

// State
const hasBookContent = ref(false)
const isLoading = ref(false)
const loadError = ref('')
const isExpanded = ref(false)
const currentPage = ref(1)
const totalPages = ref(0)
const currentChapter = ref<Chapter | null>(null)
const tableOfContents = ref<TableOfContentsItem[]>([])
const annotations = ref<Annotation[]>([])
const bookmarks = ref<Bookmark[]>([])
const bookFormat = ref<BookFormat>('')

// UI State
const showTableOfContents = ref(false)
const showBookmarks = ref(false)
const showAnnotations = ref(false)
const showSearch = ref(false)
const showSettings = ref(false)
const showBookmarkModal = ref(false)

// Refs
const readingArea = ref<HTMLElement>()
const foliateView = ref<any>()
const pdfCanvas = ref<HTMLCanvasElement>()
const textLayer = ref<HTMLElement>()

// Computed
const readingPercentage = computed(() => {
  return totalPages.value > 0 ? (currentPage.value / totalPages.value) * 100 : 0
})

const canGoPrevious = computed(() => currentPage.value > 1)
const canGoNext = computed(() => currentPage.value < totalPages.value)

const bookmarkPositions = computed(() => {
  return bookmarks.value.map(b => ({
    page: b.page_number,
    percentage: (b.page_number / totalPages.value) * 100
  }))
})

// Methods
const loadBookContent = async () => {
  try {
    isLoading.value = true
    loadError.value = ''
    
    // Check if book has content
    const bookData = await window.db.books.getById(props.book.id)
    if (!bookData.has_content) {
      hasBookContent.value = false
      return
    }
    
    hasBookContent.value = true
    bookFormat.value = bookData.book_file_format
    
    // Load table of contents
    tableOfContents.value = await window.db.books.getTableOfContents(props.book.id)
    
    // Load reading progress
    const progress = await loadProgress()
    if (progress) {
      currentPage.value = progress.currentPage || 1
    }
    
    // Load content based on format
    if (bookFormat.value === 'epub') {
      await loadEPUBContent()
    } else if (bookFormat.value === 'pdf') {
      await loadPDFContent()
    }
    
    // Load annotations and bookmarks for current page
    await loadPageAnnotations()
    
  } catch (error) {
    console.error('Failed to load book content:', error)
    loadError.value = 'Failed to load book content. Please try again.'
  } finally {
    isLoading.value = false
  }
}

const processBookFile = async (file: File) => {
  try {
    isLoading.value = true
    
    // Import the book file
    await window.db.books.importFile(props.book.id, file.path)
    
    // Reload book content
    await loadBookContent()
    
  } catch (error) {
    console.error('Failed to import book:', error)
    loadError.value = 'Failed to import book file. Please try again.'
  } finally {
    isLoading.value = false
  }
}

const loadPageAnnotations = async () => {
  annotations.value = await window.db.annotations.getForPage(props.book.id, currentPage.value)
  bookmarks.value = await window.db.bookmarks.getByBook(props.book.id)
}

const previousPage = async () => {
  if (canGoPrevious.value) {
    currentPage.value--
    await updatePage()
  }
}

const nextPage = async () => {
  if (canGoNext.value) {
    currentPage.value++
    await updatePage()
  }
}

const goToPage = async () => {
  currentPage.value = Math.max(1, Math.min(currentPage.value, totalPages.value))
  await updatePage()
}

const updatePage = async () => {
  await loadPageContent()
  await loadPageAnnotations()
  await saveProgress({
    currentPage: currentPage.value,
    percentage: readingPercentage.value
  })
}

// Auto-save progress
const progressInterval = setInterval(() => {
  if (hasBookContent.value) {
    saveProgress({
      currentPage: currentPage.value,
      percentage: readingPercentage.value
    })
  }
}, 30000) // Every 30 seconds

// Cleanup
onUnmounted(() => {
  clearInterval(progressInterval)
  if (hasBookContent.value) {
    saveProgress({
      currentPage: currentPage.value,
      percentage: readingPercentage.value
    })
  }
})

// Initialize
onMounted(() => {
  bookReaderStore.setCurrentBook(props.book)
  loadBookContent()
})
</script>
```

### 4.2 Foliate.js Integration
Create a custom element wrapper for Foliate:

```typescript
// src/lib/foliate-wrapper.ts
import { View } from 'foliate-js/view.js'

export class FoliateWrapper {
  private view: View
  private container: HTMLElement
  
  constructor(container: HTMLElement) {
    this.container = container
    this.view = new View()
    container.appendChild(this.view)
  }
  
  async open(book: any) {
    await this.view.open(book)
  }
  
  goTo(cfi: string) {
    this.view.goTo(cfi)
  }
  
  on(event: string, handler: Function) {
    this.view.addEventListener(event, handler)
  }
  
  destroy() {
    this.view.remove()
  }
}
```

## Phase 5: New Component Creation

### 5.1 ReaderSettingsModal
Create `src/components/modals/ReaderSettingsModal.vue`:

```vue
<template>
  <div class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-content reader-settings-modal">
      <div class="modal-header">
        <h3>Reading Settings</h3>
        <button class="close-button" @click="$emit('close')">
          <img src="/icons/close-icon.svg" alt="Close" />
        </button>
      </div>
      
      <div class="modal-body">
        <!-- Typography Settings -->
        <div class="settings-section">
          <h4>Typography</h4>
          
          <div class="setting-row">
            <label>Font Size</label>
            <input 
              type="range" 
              min="12" 
              max="24" 
              v-model.number="localSettings.fontSize"
              @input="updateSetting('fontSize', localSettings.fontSize)"
            />
            <span>{{ localSettings.fontSize }}px</span>
          </div>
          
          <div class="setting-row">
            <label>Font Family</label>
            <select 
              v-model="localSettings.fontFamily"
              @change="updateSetting('fontFamily', localSettings.fontFamily)"
            >
              <option value="Georgia">Georgia</option>
              <option value="Arial">Arial</option>
              <option value="Times New Roman">Times New Roman</option>
              <option value="Helvetica">Helvetica</option>
              <option value="Montserrat">Montserrat</option>
            </select>
          </div>
          
          <div class="setting-row">
            <label>Line Height</label>
            <input 
              type="range" 
              min="1.2" 
              max="2.0" 
              step="0.1"
              v-model.number="localSettings.lineHeight"
              @input="updateSetting('lineHeight', localSettings.lineHeight)"
            />
            <span>{{ localSettings.lineHeight }}</span>
          </div>
        </div>
        
        <!-- Theme Settings -->
        <div class="settings-section">
          <h4>Theme</h4>
          <div class="theme-options">
            <button 
              v-for="theme in themes" 
              :key="theme.value"
              :class="['theme-option', { active: localSettings.theme === theme.value }]"
              @click="updateSetting('theme', theme.value)"
            >
              <div class="theme-preview" :style="theme.preview"></div>
              <span>{{ theme.label }}</span>
            </button>
          </div>
        </div>
        
        <!-- Layout Settings -->
        <div class="settings-section">
          <h4>Layout</h4>
          
          <div class="setting-row">
            <label>Page Width</label>
            <input 
              type="range" 
              min="600" 
              max="1200" 
              step="50"
              v-model.number="localSettings.pageWidth"
              @input="updateSetting('pageWidth', localSettings.pageWidth)"
            />
            <span>{{ localSettings.pageWidth }}px</span>
          </div>
          
          <div class="setting-row">
            <label>Margins</label>
            <input 
              type="range" 
              min="10" 
              max="50" 
              step="5"
              v-model.number="localSettings.margin"
              @input="updateSetting('margin', localSettings.margin)"
            />
            <span>{{ localSettings.margin }}px</span>
          </div>
          
          <div class="setting-row">
            <label>Reading Mode</label>
            <select 
              v-model="localSettings.flow"
              @change="updateSetting('flow', localSettings.flow)"
            >
              <option value="paginated">Paginated</option>
              <option value="scrolled">Continuous Scroll</option>
            </select>
          </div>
        </div>
      </div>
      
      <div class="modal-footer">
        <button class="btn btn-secondary" @click="resetToDefaults">Reset to Defaults</button>
        <button class="btn btn-primary" @click="saveSettings">Save Settings</button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import type { ReadingSettings } from '@/types'

const props = defineProps<{
  settings: ReadingSettings
}>()

const emit = defineEmits<{
  update: [settings: ReadingSettings]
  close: []
}>()

const localSettings = ref({ ...props.settings })

const themes = [
  { 
    value: 'light', 
    label: 'Light',
    preview: { background: '#ffffff', color: '#000000' }
  },
  { 
    value: 'dark', 
    label: 'Dark',
    preview: { background: '#1e1e1e', color: '#ffffff' }
  },
  { 
    value: 'sepia', 
    label: 'Sepia',
    preview: { background: '#f4ecd8', color: '#5c4b37' }
  }
]

const updateSetting = (key: string, value: any) => {
  // Real-time preview
  emit('update', { ...localSettings.value })
}

const resetToDefaults = () => {
  localSettings.value = {
    fontSize: 16,
    fontFamily: 'Georgia',
    lineHeight: 1.6,
    theme: 'light',
    pageWidth: 800,
    margin: 20,
    flow: 'paginated',
    maxColumnCount: 1
  }
  emit('update', localSettings.value)
}

const saveSettings = () => {
  emit('update', localSettings.value)
  emit('close')
}

const handleOverlayClick = (event: MouseEvent) => {
  if (event.target === event.currentTarget) {
    emit('close')
  }
}
</script>
```

### 5.2 BookmarksPanel
Create `src/components/reader/BookmarksPanel.vue`:

```vue
<template>
  <div class="bookmarks-panel">
    <div class="panel-header">
      <h4>Bookmarks</h4>
      <button class="add-bookmark-btn" @click="$emit('add-bookmark')">
        <img src="/icons/plus-icon.svg" alt="Add" />
        Add Bookmark
      </button>
    </div>
    
    <div class="bookmarks-list">
      <div v-if="bookmarks.length === 0" class="empty-state">
        <p>No bookmarks yet</p>
        <p class="hint">Click "Add Bookmark" to save your current position</p>
      </div>
      
      <div 
        v-for="bookmark in sortedBookmarks" 
        :key="bookmark.id"
        class="bookmark-item"
        :class="{ active: isCurrentPage(bookmark) }"
        @click="$emit('navigate', bookmark)"
      >
        <div class="bookmark-color" :style="{ backgroundColor: bookmark.color }"></div>
        <div class="bookmark-info">
          <div class="bookmark-title">{{ bookmark.name || `Page ${bookmark.page_number}` }}</div>
          <div v-if="bookmark.chapter_title" class="bookmark-chapter">{{ bookmark.chapter_title }}</div>
          <div class="bookmark-page">Page {{ bookmark.page_number }}</div>
        </div>
        <div class="bookmark-actions">
          <button @click.stop="$emit('edit', bookmark)" title="Edit">
            <img src="/icons/edit-icon.svg" alt="Edit" />
          </button>
          <button @click.stop="$emit('delete', bookmark)" title="Delete">
            <img src="/icons/trash-icon.svg" alt="Delete" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import type { Bookmark } from '@/types'

const props = defineProps<{
  bookmarks: Bookmark[]
  currentPage: number
}>()

const emit = defineEmits<{
  navigate: [bookmark: Bookmark]
  edit: [bookmark: Bookmark]
  delete: [bookmark: Bookmark]
  'add-bookmark': []
}>()

const sortedBookmarks = computed(() => {
  return [...props.bookmarks].sort((a, b) => a.page_number - b.page_number)
})

const isCurrentPage = (bookmark: Bookmark) => {
  return bookmark.page_number === props.currentPage
}
</script>
```

### 5.3 AnnotationToolbar
Create `src/components/reader/AnnotationToolbar.vue`:

```vue
<template>
  <div 
    class="annotation-toolbar"
    :style="toolbarStyle"
    v-if="selection"
  >
    <!-- Quick Actions -->
    <div class="toolbar-section quick-actions">
      <button
        v-for="color in highlightColors"
        :key="color"
        class="color-button"
        :style="{ backgroundColor: color }"
        @click="createHighlight(color)"
        :title="`Highlight in ${getColorName(color)}`"
      />
    </div>
    
    <div class="toolbar-divider" />
    
    <!-- Annotation Types -->
    <div class="toolbar-section annotation-types">
      <button
        class="toolbar-button"
        @click="createAnnotation('underline')"
        title="Underline"
      >
        <img src="/icons/underline-icon.svg" alt="Underline" />
      </button>
      
      <button
        class="toolbar-button"
        @click="createAnnotation('note')"
        title="Add Note"
      >
        <img src="/icons/notes-icon.svg" alt="Note" />
      </button>
      
      <button
        class="toolbar-button"
        @click="createBookmark()"
        title="Bookmark"
      >
        <img src="/icons/bookmark-icon.svg" alt="Bookmark" />
      </button>
    </div>
    
    <div class="toolbar-divider" />
    
    <!-- Advanced Actions -->
    <div class="toolbar-section advanced-actions">
      <button
        class="toolbar-button"
        @click="createFullNote()"
        title="Create Full Note"
      >
        <img src="/icons/expand-icon.svg" alt="Expand" />
        <span>Expand to Note</span>
      </button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import type { TextSelection } from '@/types'

const props = defineProps<{
  selection: TextSelection
  position: { x: number; y: number }
}>()

const emit = defineEmits<{
  highlight: [color: string]
  note: [text: string]
  bookmark: []
}>()

const router = useRouter()

const highlightColors = [
  '#FFEB3B', // Yellow
  '#81C784', // Green
  '#64B5F6', // Blue
  '#FFB74D', // Orange
  '#F06292'  // Pink
]

const toolbarStyle = computed(() => {
  return {
    position: 'fixed',
    left: `${props.position.x}px`,
    top: `${props.position.y - 48}px`,
    transform: 'translateX(-50%)'
  }
})

const createHighlight = (color: string) => {
  emit('highlight', color)
}

const createAnnotation = (type: string) => {
  emit('note', props.selection.text)
}

const createBookmark = () => {
  emit('bookmark')
}

const createFullNote = async () => {
  // Create annotation first
  const annotation = await window.db.annotations.create({
    book_id: props.bookId,
    type: 'note',
    selected_text: props.selection.text,
    start_position: props.selection.startPosition,
    end_position: props.selection.endPosition,
    page_number: props.currentPage
  })
  
  // Convert to note
  const note = await window.db.annotations.convertToNote(annotation.id)
  
  // Navigate to note editor
  router.push({ name: 'NoteEditor', params: { noteId: note.id } })
}

const getColorName = (color: string): string => {
  const colorNames: Record<string, string> = {
    '#FFEB3B': 'Yellow',
    '#81C784': 'Green',
    '#64B5F6': 'Blue',
    '#FFB74D': 'Orange',
    '#F06292': 'Pink'
  }
  return colorNames[color] || 'Color'
}
</script>
```

### 5.4 Additional Components
Continue creating the remaining components:
- `AnnotationsSidebar.vue` - Display all annotations
- `ReadingProgress.vue` - Visual progress bar
- `TableOfContents.vue` - Chapter navigation

## Phase 6: State Management

### 6.1 Create Book Reader Store
Create `src/stores/bookReaderStore.ts`:

```typescript
import { defineStore } from 'pinia'
import type { Book, Chapter, Annotation, Bookmark, ReadingSettings } from '@/types'

export const useBookReaderStore = defineStore('bookReader', {
  state: () => ({
    // Current book data
    currentBook: null as Book | null,
    bookContent: null as BookContent | null,
    
    // Reading position
    currentPage: 1,
    currentChapter: null as Chapter | null,
    readingPosition: {} as ReadingPosition,
    
    // UI state
    viewMode: 'single' as ViewMode,
    zoom: 100,
    isExpanded: false,
    
    // User data
    annotations: [] as Annotation[],
    bookmarks: [] as Bookmark[],
    readingSettings: {
      fontSize: 16,
      fontFamily: 'Georgia',
      lineHeight: 1.6,
      theme: 'light',
      pageWidth: 800,
      margin: 20,
      flow: 'paginated',
      maxColumnCount: 1
    } as ReadingSettings,
    
    // Search
    searchResults: [] as SearchResult[],
    currentSearchQuery: ''
  }),
  
  getters: {
    readingPercentage: (state) => {
      if (!state.bookContent) return 0
      return (state.currentPage / state.bookContent.totalPages) * 100
    },
    
    currentPageAnnotations: (state) => {
      return state.annotations.filter(a => a.page_number === state.currentPage)
    },
    
    bookmarkForCurrentPage: (state) => {
      return state.bookmarks.find(b => b.page_number === state.currentPage)
    }
  },
  
  actions: {
    async setCurrentBook(book: Book) {
      this.currentBook = book
      await this.loadBookContent()
      await this.loadReadingProgress()
      await this.loadReadingSettings()
    },
    
    async loadBookContent() {
      if (!this.currentBook?.id) return
      
      try {
        // Load book content metadata
        const content = await window.db.books.getContent(this.currentBook.id)
        this.bookContent = content
        
        // Load table of contents
        const toc = await window.db.books.getTableOfContents(this.currentBook.id)
        this.bookContent.tableOfContents = toc
        
      } catch (error) {
        console.error('Failed to load book content:', error)
      }
    },
    
    async loadReadingProgress() {
      if (!this.currentBook?.id) return
      
      try {
        const progress = await window.db.books.getReadingProgress(this.currentBook.id)
        if (progress) {
          this.currentPage = progress.currentPage || 1
          this.readingPosition = progress.position || {}
        }
      } catch (error) {
        console.error('Failed to load reading progress:', error)
      }
    },
    
    async loadReadingSettings() {
      if (!this.currentBook?.id) return
      
      try {
        const settings = await window.db.books.getReadingSettings(this.currentBook.id)
        if (settings) {
          this.readingSettings = { ...this.readingSettings, ...settings }
        }
      } catch (error) {
        console.error('Failed to load reading settings:', error)
      }
    },
    
    async saveReadingProgress() {
      if (!this.currentBook?.id) return
      
      try {
        await window.db.books.updateReadingProgress(this.currentBook.id, {
          currentPage: this.currentPage,
          percentage: this.readingPercentage,
          position: this.readingPosition,
          lastReadAt: new Date().toISOString()
        })
      } catch (error) {
        console.error('Failed to save reading progress:', error)
      }
    },
    
    async saveReadingSettings() {
      if (!this.currentBook?.id) return
      
      try {
        await window.db.books.saveReadingSettings(this.currentBook.id, this.readingSettings)
      } catch (error) {
        console.error('Failed to save reading settings:', error)
      }
    },
    
    async createAnnotation(data: CreateAnnotationData) {
      if (!this.currentBook?.id) return
      
      try {
        const annotation = await window.db.annotations.create({
          ...data,
          book_id: this.currentBook.id
        })
        
        this.annotations.push(annotation)
        
        // Update annotation count
        if (this.currentBook) {
          this.currentBook.annotation_count = (this.currentBook.annotation_count || 0) + 1
        }
        
        return annotation
      } catch (error) {
        console.error('Failed to create annotation:', error)
        throw error
      }
    },
    
    async createBookmark(data: CreateBookmarkData) {
      if (!this.currentBook?.id) return
      
      try {
        const bookmark = await window.db.bookmarks.create({
          ...data,
          book_id: this.currentBook.id
        })
        
        this.bookmarks.push(bookmark)
        
        // Update bookmark count
        if (this.currentBook) {
          this.currentBook.bookmark_count = (this.currentBook.bookmark_count || 0) + 1
        }
        
        return bookmark
      } catch (error) {
        console.error('Failed to create bookmark:', error)
        throw error
      }
    },
    
    async searchInBook(query: string) {
      if (!this.currentBook?.id || !query) return
      
      try {
        this.currentSearchQuery = query
        this.searchResults = await window.db.books.search(this.currentBook.id, query)
      } catch (error) {
        console.error('Failed to search in book:', error)
      }
    },
    
    updateReadingSettings(settings: Partial<ReadingSettings>) {
      this.readingSettings = { ...this.readingSettings, ...settings }
      this.saveReadingSettings()
    },
    
    toggleExpanded() {
      this.isExpanded = !this.isExpanded
    }
  }
})
```

### 6.2 Update Settings Store
Add reading preferences to `src/stores/settingsStore.ts`:

```typescript
// Add to existing settings interface
interface AppSettings {
  // ... existing settings
  
  // Book reading defaults
  defaultReadingTheme: 'light' | 'dark' | 'sepia'
  defaultFontSize: number
  defaultFontFamily: string
  defaultLineHeight: number
  autoSaveReadingProgress: boolean
  readingProgressInterval: number // minutes
}

// Add to default settings
const defaultSettings: AppSettings = {
  // ... existing defaults
  
  defaultReadingTheme: 'light',
  defaultFontSize: 16,
  defaultFontFamily: 'Georgia',
  defaultLineHeight: 1.6,
  autoSaveReadingProgress: true,
  readingProgressInterval: 5
}
```

## Phase 7: Annotation System

### 7.1 Text Selection Composable
Create `src/composables/useTextSelection.ts`:

```typescript
import { ref, onMounted, onUnmounted } from 'vue'

export interface TextSelection {
  text: string
  range: Range
  rect: DOMRect
  startPosition: SelectionPosition
  endPosition: SelectionPosition
}

export interface SelectionPosition {
  node: string
  offset: number
  paragraph?: number
  sentence?: number
  word?: number
}

export function useTextSelection() {
  const selection = ref<TextSelection | null>(null)
  const isSelecting = ref(false)
  
  const handleSelection = () => {
    const sel = window.getSelection()
    if (!sel || sel.isCollapsed) {
      selection.value = null
      return
    }
    
    const range = sel.getRangeAt(0)
    const rect = range.getBoundingClientRect()
    
    // Get precise position data
    const startPosition = getNodePosition(range.startContainer, range.startOffset)
    const endPosition = getNodePosition(range.endContainer, range.endOffset)
    
    selection.value = {
      text: sel.toString(),
      range: range.cloneRange(),
      rect,
      startPosition,
      endPosition
    }
  }
  
  const clearSelection = () => {
    window.getSelection()?.removeAllRanges()
    selection.value = null
  }
  
  const getNodePosition = (node: Node, offset: number): SelectionPosition => {
    // Calculate position within the document structure
    const path = getNodePath(node)
    const paragraph = findParagraphIndex(node)
    const sentence = findSentenceIndex(node, offset)
    const word = findWordIndex(node, offset)
    
    return {
      node: path,
      offset,
      paragraph,
      sentence,
      word
    }
  }
  
  const getNodePath = (node: Node): string => {
    const path: string[] = []
    let current: Node | null = node
    
    while (current && current !== document.body) {
      if (current.nodeType === Node.ELEMENT_NODE) {
        const element = current as Element
        const index = Array.from(element.parentNode?.children || []).indexOf(element)
        path.unshift(`${element.tagName.toLowerCase()}[${index}]`)
      }
      current = current.parentNode
    }
    
    return path.join('/')
  }
  
  // Event listeners
  onMounted(() => {
    document.addEventListener('selectionchange', handleSelection)
    document.addEventListener('mouseup', handleSelection)
  })
  
  onUnmounted(() => {
    document.removeEventListener('selectionchange', handleSelection)
    document.removeEventListener('mouseup', handleSelection)
  })
  
  return {
    selection,
    isSelecting,
    clearSelection
  }
}
```

### 7.2 Annotation Renderer
Create `src/lib/annotation-renderer.ts`:

```typescript
export class AnnotationRenderer {
  private container: HTMLElement
  private annotations: Annotation[]
  
  constructor(container: HTMLElement) {
    this.container = container
    this.annotations = []
  }
  
  setAnnotations(annotations: Annotation[]) {
    this.annotations = annotations
    this.render()
  }
  
  render() {
    // Remove existing annotations
    this.container.querySelectorAll('.annotation').forEach(el => {
      el.classList.remove('annotation', 'annotation-highlight', 'annotation-underline')
    })
    
    // Apply annotations
    for (const annotation of this.annotations) {
      this.applyAnnotation(annotation)
    }
  }
  
  private applyAnnotation(annotation: Annotation) {
    try {
      const range = this.createRangeFromPosition(annotation.start_position, annotation.end_position)
      if (!range) return
      
      // Create wrapper element
      const wrapper = document.createElement('span')
      wrapper.className = `annotation annotation-${annotation.type}`
      wrapper.dataset.annotationId = annotation.id.toString()
      wrapper.style.setProperty('--annotation-color', annotation.color || '#FFEB3B')
      
      // Apply to range
      try {
        range.surroundContents(wrapper)
      } catch (error) {
        // If surroundContents fails (e.g., range spans multiple elements),
        // extract and wrap contents manually
        const contents = range.extractContents()
        wrapper.appendChild(contents)
        range.insertNode(wrapper)
      }
      
      // Add event listeners
      wrapper.addEventListener('click', () => this.handleAnnotationClick(annotation))
      wrapper.addEventListener('contextmenu', (e) => this.handleAnnotationRightClick(e, annotation))
      
    } catch (error) {
      console.error('Failed to apply annotation:', error)
    }
  }
  
  private createRangeFromPosition(start: SelectionPosition, end: SelectionPosition): Range | null {
    try {
      const range = document.createRange()
      
      // Find nodes from stored paths
      const startNode = this.findNodeFromPath(start.node)
      const endNode = this.findNodeFromPath(end.node)
      
      if (!startNode || !endNode) return null
      
      range.setStart(startNode, start.offset)
      range.setEnd(endNode, end.offset)
      
      return range
    } catch (error) {
      console.error('Failed to create range from position:', error)
      return null
    }
  }
  
  private findNodeFromPath(path: string): Node | null {
    // Implement path resolution logic
    // This would parse the path string and traverse the DOM to find the node
    return null
  }
  
  private handleAnnotationClick(annotation: Annotation) {
    // Emit event for annotation click
    this.container.dispatchEvent(new CustomEvent('annotation-click', {
      detail: annotation
    }))
  }
  
  private handleAnnotationRightClick(event: MouseEvent, annotation: Annotation) {
    event.preventDefault()
    // Emit event for context menu
    this.container.dispatchEvent(new CustomEvent('annotation-context-menu', {
      detail: { annotation, x: event.clientX, y: event.clientY }
    }))
  }
}
```

### 7.3 Annotation to Note Conversion
Create `src/lib/annotation-note-converter.ts`:

```typescript
export class AnnotationNoteConverter {
  static async convertToNote(annotation: Annotation, book: Book): Promise<Note> {
    const noteContent = this.generateNoteContent(annotation, book)
    
    // Create note with annotation reference
    const note = await window.db.notes.create({
      title: this.generateNoteTitle(annotation, book),
      content: noteContent.markdown,
      html_content: noteContent.html,
      book_id: book.id,
      annotation_id: annotation.id,
      reading_context: JSON.stringify({
        page: annotation.page_number,
        chapter: annotation.chapter_title,
        selected_text: annotation.selected_text,
        position: annotation.start_position
      })
    })
    
    // Update annotation to link to note
    await window.db.annotations.update(annotation.id, { note_id: note.id })
    
    return note
  }
  
  private static generateNoteTitle(annotation: Annotation, book: Book): string {
    const date = new Date().toLocaleDateString()
    const excerpt = annotation.selected_text.substring(0, 50)
    return `${book.title} - "${excerpt}..." (${date})`
  }
  
  private static generateNoteContent(annotation: Annotation, book: Book): { markdown: string, html: string } {
    const markdown = `
# Note on "${book.title}"

## Highlighted Text

> ${annotation.selected_text}

## My Thoughts

${annotation.content || '_(Add your thoughts here)_'}

---

_Page ${annotation.page_number}${annotation.chapter_title ? ` • ${annotation.chapter_title}` : ''}_
_Created: ${new Date().toLocaleString()}_
    `.trim()
    
    // Convert markdown to HTML (using existing markdown parser)
    const html = marked.parse(markdown)
    
    return { markdown, html }
  }
}
```

## Phase 8: Sync Integration

### 8.1 Extend Manifest Types
Update `electron/main/api/sync-logic/types.ts`:

```typescript
// Add to existing ManifestItem union type
export interface BookContentManifestItem extends BaseManifestItem {
  type: 'book-content'
  relationships: {
    bookId: string
  }
  metadata: {
    format: BookFormat
    fileSize: number
    fileHash: string
    totalPages: number
    totalChapters: number
  }
}

export interface AnnotationManifestItem extends BaseManifestItem {
  type: 'annotation'
  relationships: {
    bookId: string
    noteId?: string
  }
  metadata: {
    annotationType: 'highlight' | 'note' | 'underline' | 'comment'
    color?: string
    selectedText?: string
    pageNumber?: number
    chapterId?: string
    startPosition?: string
    endPosition?: string
  }
}

export interface BookmarkManifestItem extends BaseManifestItem {
  type: 'bookmark'
  relationships: {
    bookId: string
  }
  metadata: {
    name?: string
    description?: string
    pageNumber?: number
    chapterId?: string
    positionData?: string
    color?: string
  }
}

export interface ReadingProgressManifestItem extends BaseManifestItem {
  type: 'reading-progress'
  relationships: {
    bookId: string
  }
  metadata: {
    lastReadAt: string
    currentPage?: number
    progressPercentage: number
    totalReadingTime: number
    position?: string
  }
}
```

### 8.2 Book Content Sync
Create `electron/main/api/sync-logic/book-content-sync.ts`:

```typescript
export class BookContentSync {
  private readonly CHUNK_SIZE = 5 * 1024 * 1024 // 5MB chunks
  
  async syncBookContent(book: Book, direction: 'export' | 'import'): Promise<void> {
    if (direction === 'export') {
      await this.exportBookContent(book)
    } else {
      await this.importBookContent(book)
    }
  }
  
  private async exportBookContent(book: Book): Promise<void> {
    if (!book.has_content) return
    
    const content = await db.get('SELECT * FROM book_content WHERE book_id = ?', [book.id])
    if (!content) return
    
    // Create book content directory in sync folder
    const bookSyncPath = path.join(this.syncDirectory, 'Books', book.title, 'content')
    await fs.mkdir(bookSyncPath, { recursive: true })
    
    // Export original file in chunks
    if (content.original_file) {
      await this.exportFileInChunks(
        content.original_file,
        path.join(bookSyncPath, `book.${content.format}`)
      )
    }
    
    // Export chapters
    const chapters = await db.all('SELECT * FROM book_chapters WHERE book_id = ?', [book.id])
    for (const chapter of chapters) {
      await fs.writeFile(
        path.join(bookSyncPath, 'chapters', `chapter-${chapter.chapter_number}.json`),
        JSON.stringify(chapter),
        'utf8'
      )
    }
    
    // Export pages (for PDFs)
    const pages = await db.all('SELECT * FROM book_pages WHERE book_id = ?', [book.id])
    for (const page of pages) {
      await fs.writeFile(
        path.join(bookSyncPath, 'pages', `page-${page.page_number}.json`),
        JSON.stringify(page),
        'utf8'
      )
    }
    
    // Create manifest entry
    const manifestItem: BookContentManifestItem = {
      id: `book-content-${book.id}`,
      type: 'book-content',
      name: `${book.title} - Content`,
      path: `Books/${book.title}/content`,
      hash: content.file_hash,
      modified: new Date().toISOString(),
      relationships: {
        bookId: `book_${book.id}`
      },
      metadata: {
        format: content.format,
        fileSize: content.original_file.length,
        fileHash: content.file_hash,
        totalPages: content.total_pages,
        totalChapters: content.total_chapters
      }
    }
    
    await this.manifest.addItem(manifestItem)
  }
  
  private async exportFileInChunks(file: Buffer, filePath: string): Promise<void> {
    const totalChunks = Math.ceil(file.length / this.CHUNK_SIZE)
    
    for (let i = 0; i < totalChunks; i++) {
      const start = i * this.CHUNK_SIZE
      const end = Math.min(start + this.CHUNK_SIZE, file.length)
      const chunk = file.slice(start, end)
      
      await fs.writeFile(`${filePath}.chunk${i}`, chunk)
      
      // Emit progress
      this.emitProgress({
        phase: 'exporting',
        current: `Exporting book chunk ${i + 1}/${totalChunks}`,
        progress: ((i + 1) / totalChunks) * 100
      })
    }
    
    // Write chunk info
    await fs.writeFile(`${filePath}.info`, JSON.stringify({
      totalChunks,
      fileSize: file.length,
      chunkSize: this.CHUNK_SIZE
    }), 'utf8')
  }
}
```

### 8.3 Annotation & Bookmark Sync
```typescript
export class AnnotationSync {
  async syncAnnotations(bookId: number): Promise<void> {
    const localAnnotations = await db.all('SELECT * FROM book_annotations WHERE book_id = ?', [bookId])
    const remoteAnnotations = await this.getRemoteAnnotations(bookId)
    
    // Detect changes
    const changes = this.detectChanges(localAnnotations, remoteAnnotations)
    
    // Apply changes
    for (const change of changes.toImport) {
      await this.importAnnotation(change)
    }
    
    for (const change of changes.toExport) {
      await this.exportAnnotation(change)
    }
    
    // Handle conflicts
    for (const conflict of changes.conflicts) {
      await this.resolveConflict(conflict)
    }
  }
  
  private detectChanges(local: Annotation[], remote: AnnotationManifestItem[]): AnnotationChanges {
    // Implementation for change detection
    // Compare by ID, modification time, and content
  }
  
  private async resolveConflict(conflict: AnnotationConflict): Promise<void> {
    // Auto-resolution rules:
    // 1. If same text, different positions - keep both
    // 2. If different colors - use most recent
    // 3. If different types - keep both
    // 4. If notes differ significantly - keep both
    
    const resolution = this.autoResolve(conflict)
    
    switch (resolution) {
      case 'keep_local':
        // Export local version
        await this.exportAnnotation(conflict.local)
        break
      case 'keep_remote':
        // Import remote version
        await this.importAnnotation(conflict.remote)
        break
      case 'keep_both':
        // Create duplicate with different ID
        await this.createDuplicate(conflict)
        break
      case 'merge':
        // Merge annotations
        await this.mergeAnnotations(conflict)
        break
    }
  }
}
```

### 8.4 Reading Progress Sync
```typescript
export class ReadingProgressSync {
  async syncProgress(bookId: number): Promise<void> {
    const localProgress = await this.getLocalProgress(bookId)
    const remoteProgress = await this.getRemoteProgress(bookId)
    
    if (!remoteProgress || localProgress.lastReadAt > remoteProgress.lastReadAt) {
      // Local is newer
      await this.exportProgress(bookId, localProgress)
    } else if (localProgress.lastReadAt < remoteProgress.lastReadAt) {
      // Remote is newer
      await this.importProgress(bookId, remoteProgress)
    } else {
      // Same timestamp - merge statistics
      const merged = this.mergeProgress(localProgress, remoteProgress)
      await this.saveProgress(bookId, merged)
    }
  }
  
  private mergeProgress(local: ReadingProgress, remote: ReadingProgress): ReadingProgress {
    return {
      ...local,
      totalReadingTime: Math.max(local.totalReadingTime, remote.totalReadingTime),
      currentPage: Math.max(local.currentPage, remote.currentPage),
      progressPercentage: Math.max(local.progressPercentage, remote.progressPercentage)
    }
  }
}
```

## Phase 9: Testing and Polish

### 9.1 Test Plan

#### Unit Tests
```typescript
// tests/book-parser.test.ts
describe('BookParser', () => {
  it('should parse EPUB files correctly', async () => {
    const parser = new BookParser()
    const result = await parser.parseBook('fixtures/test.epub')
    
    expect(result.format).toBe('epub')
    expect(result.metadata.title).toBe('Test Book')
    expect(result.chapters).toHaveLength(10)
  })
  
  it('should handle corrupted files gracefully', async () => {
    const parser = new BookParser()
    await expect(parser.parseBook('fixtures/corrupted.epub'))
      .rejects.toThrow('File appears to be corrupted')
  })
})
```

#### Integration Tests
```typescript
// tests/book-reading-flow.test.ts
describe('Book Reading Flow', () => {
  it('should import book and display content', async () => {
    // Create test book
    const book = await createTestBook()
    
    // Import book file
    await importBookFile(book.id, 'fixtures/sample.epub')
    
    // Open book reader
    const reader = await openBookReader(book)
    
    // Verify content is displayed
    expect(reader.hasContent).toBe(true)
    expect(reader.totalPages).toBeGreaterThan(0)
  })
  
  it('should save and restore reading progress', async () => {
    const book = await createTestBook()
    await importBookFile(book.id, 'fixtures/sample.epub')
    
    // Set reading progress
    await updateReadingProgress(book.id, { currentPage: 50, percentage: 25 })
    
    // Reload and verify
    const progress = await getReadingProgress(book.id)
    expect(progress.currentPage).toBe(50)
    expect(progress.percentage).toBe(25)
  })
})
```

### 9.2 Performance Optimization

#### Content Caching
```typescript
export class BookContentCache {
  private cache: LRUCache<string, CachedContent>
  private preloadQueue: Set<string>
  
  constructor(maxSize = 50 * 1024 * 1024) { // 50MB
    this.cache = new LRUCache({
      max: maxSize,
      length: (item) => item.size,
      dispose: (key, item) => this.cleanup(item)
    })
    
    this.preloadQueue = new Set()
  }
  
  async getChapter(bookId: number, chapterId: number): Promise<ChapterContent> {
    const key = `chapter:${bookId}:${chapterId}`
    
    let cached = this.cache.get(key)
    if (cached) return cached.content
    
    // Load from database
    const content = await window.db.books.getChapter(bookId, chapterId)
    
    // Cache it
    this.cache.set(key, {
      content,
      size: this.calculateSize(content)
    })
    
    // Preload adjacent chapters
    this.preloadAdjacent(bookId, chapterId)
    
    return content
  }
  
  private async preloadAdjacent(bookId: number, chapterId: number) {
    const adjacentIds = [chapterId - 1, chapterId + 1].filter(id => id > 0)
    
    for (const id of adjacentIds) {
      const key = `chapter:${bookId}:${id}`
      if (!this.cache.has(key) && !this.preloadQueue.has(key)) {
        this.preloadQueue.add(key)
        
        // Preload in background
        setTimeout(() => {
          this.getChapter(bookId, id).then(() => {
            this.preloadQueue.delete(key)
          })
        }, 100)
      }
    }
  }
}
```

#### Virtual Scrolling
```typescript
export function useVirtualScroll(options: VirtualScrollOptions) {
  const visibleItems = ref<any[]>([])
  const scrollTop = ref(0)
  const containerHeight = ref(0)
  
  const totalHeight = computed(() => options.items.length * options.itemHeight)
  
  const visibleRange = computed(() => {
    const start = Math.floor(scrollTop.value / options.itemHeight)
    const end = Math.ceil((scrollTop.value + containerHeight.value) / options.itemHeight)
    
    return {
      start: Math.max(0, start - options.buffer),
      end: Math.min(options.items.length, end + options.buffer)
    }
  })
  
  watch(visibleRange, (range) => {
    visibleItems.value = options.items.slice(range.start, range.end)
    options.onVisibleChange?.(visibleItems.value)
  })
  
  return {
    visibleItems,
    totalHeight,
    handleScroll: (event: Event) => {
      const target = event.target as HTMLElement
      scrollTop.value = target.scrollTop
      containerHeight.value = target.clientHeight
    }
  }
}
```

### 9.3 Error Handling

#### Import Error Handler
```typescript
export class BookImportErrorHandler {
  async handleImportError(error: Error, filePath: string): Promise<ImportErrorResolution> {
    if (error instanceof UnsupportedFormatError) {
      return {
        message: `Format not supported: ${error.format}`,
        actions: ['cancel'],
        suggestion: 'Try converting to EPUB or PDF format'
      }
    }
    
    if (error instanceof CorruptedFileError) {
      return {
        message: 'File appears to be corrupted',
        actions: ['retry', 'cancel'],
        suggestion: 'Try downloading the file again'
      }
    }
    
    if (error instanceof FileTooLargeError) {
      return {
        message: `File too large: ${formatBytes(error.size)}`,
        actions: ['cancel'],
        suggestion: `Maximum file size is ${formatBytes(MAX_FILE_SIZE)}`
      }
    }
    
    // Generic error
    return {
      message: error.message,
      actions: ['retry', 'cancel'],
      suggestion: 'Check the file and try again'
    }
  }
}
```

## Technical Specifications

### File Size Limits
- Maximum book file: 500MB
- Maximum cover image: 10MB
- Cache size per book: 50MB
- Total cache size: 500MB

### Performance Targets
- Page load time: < 100ms
- Annotation creation: < 50ms
- Search results: < 200ms
- Sync time (average book): < 30s

### Browser/Platform Requirements
- Electron 25+
- Node.js 16+
- SQLite with FTS5 extension
- Modern browser features (IntersectionObserver, ResizeObserver)

### Security Considerations
- Content Security Policy for rendered book content
- Input sanitization for annotations
- Path traversal prevention for file operations
- XSS prevention in book content rendering

## Risk Mitigation

### Technical Risks
1. **Large File Handling**: Implement streaming and chunking
2. **Memory Leaks**: Proper cleanup of rendered content
3. **Sync Conflicts**: Robust conflict resolution
4. **Format Compatibility**: Graceful fallbacks for unsupported features

### User Experience Risks
1. **Data Loss**: Auto-save every 30 seconds
2. **Import Failures**: Clear error messages and retry options
3. **Performance Issues**: Progressive loading and caching
4. **Accessibility**: Full keyboard navigation and screen reader support

## Success Metrics

### Functional Requirements
- ✓ Import and display EPUB files
- ✓ Import and display PDF files
- ✓ Create and manage annotations
- ✓ Save and sync reading progress
- ✓ Search within books
- ✓ Convert annotations to notes

### Performance Requirements
- Page load time < 100ms for 95% of requests
- Memory usage < 200MB for typical books
- Smooth scrolling at 60 FPS
- Search results within 200ms

### User Experience Requirements
- Zero data loss
- Intuitive annotation creation
- Seamless sync across devices
- Accessible to users with disabilities

## Conclusion

This implementation plan provides a comprehensive roadmap for adding professional-grade book reading functionality to Noti. The phased approach ensures that each component can be developed and tested independently while maintaining backward compatibility with existing features.

The system leverages existing infrastructure wherever possible, follows established patterns, and introduces new capabilities in a way that enhances rather than disrupts the current user experience. With careful attention to performance, security, and user experience, this implementation will transform Noti into a complete knowledge management platform that seamlessly integrates reading, annotation, and note-taking.