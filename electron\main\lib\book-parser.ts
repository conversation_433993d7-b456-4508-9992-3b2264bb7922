import * as fs from 'fs/promises';
import * as path from 'path';
import * as crypto from 'crypto';
import EPub from 'epubjs';
import { getDocument, GlobalWorkerOptions } from 'pdfjs-dist';
import JSZip from 'jszip';

// Set worker path for PDF.js
GlobalWorkerOptions.workerSrc = require('pdfjs-dist/build/pdf.worker.entry');

export interface BookMetadata {
    title: string;
    author: string;
    publisher?: string;
    publishedDate?: string;
    language?: string;
    isbn?: string;
    description?: string;
    coverUrl?: string;
    pageCount?: number;
    format: 'epub' | 'pdf' | 'mobi' | 'azw3' | 'fb2' | 'cbz';
}

export interface ChapterInfo {
    id: string;
    title: string;
    href: string;
    order: number;
    startPage?: number;
    endPage?: number;
    level?: number;
    parent?: string;
}

export interface PageContent {
    pageNumber: number;
    chapterId?: string;
    contentHtml?: string;
    contentText: string;
    imageData?: Buffer;
    width?: number;
    height?: number;
}

export interface ParsedBook {
    metadata: BookMetadata;
    chapters: ChapterInfo[];
    pages: PageContent[];
    tableOfContents: any;
    totalPages: number;
    fileHash: string;
    originalFile: Buffer;
}

export class BookParser {
    private static instance: BookParser;

    private constructor() {}

    static getInstance(): BookParser {
        if (!BookParser.instance) {
            BookParser.instance = new BookParser();
        }
        return BookParser.instance;
    }

    async parseBook(filePath: string): Promise<ParsedBook> {
        const fileBuffer = await fs.readFile(filePath);
        const fileExt = path.extname(filePath).toLowerCase().slice(1);
        const fileHash = crypto.createHash('sha256').update(fileBuffer).digest('hex');

        switch (fileExt) {
            case 'epub':
                return this.parseEpub(filePath, fileBuffer, fileHash);
            case 'pdf':
                return this.parsePdf(filePath, fileBuffer, fileHash);
            case 'mobi':
            case 'azw3':
                throw new Error(`${fileExt.toUpperCase()} format parsing not yet implemented`);
            case 'fb2':
                throw new Error('FB2 format parsing not yet implemented');
            case 'cbz':
                throw new Error('CBZ format parsing not yet implemented');
            default:
                throw new Error(`Unsupported file format: ${fileExt}`);
        }
    }

    private async parseEpub(filePath: string, fileBuffer: Buffer, fileHash: string): Promise<ParsedBook> {
        const book = EPub(filePath);
        await book.ready;

        // Get metadata
        const metadata: BookMetadata = {
            title: book.packaging.metadata.title || 'Unknown Title',
            author: book.packaging.metadata.creator || 'Unknown Author',
            publisher: book.packaging.metadata.publisher,
            publishedDate: book.packaging.metadata.pubdate,
            language: book.packaging.metadata.language,
            description: book.packaging.metadata.description,
            format: 'epub',
            coverUrl: book.cover
        };

        // Get table of contents
        const navigation = await book.loaded.navigation;
        const chapters: ChapterInfo[] = [];
        const pages: PageContent[] = [];
        
        // Process navigation/chapters
        let chapterOrder = 0;
        const processNavItem = async (item: any, level: number = 0, parent?: string) => {
            const chapter: ChapterInfo = {
                id: item.id || `chapter-${chapterOrder}`,
                title: item.label || item.title || `Chapter ${chapterOrder + 1}`,
                href: item.href,
                order: chapterOrder++,
                level,
                parent
            };
            chapters.push(chapter);

            if (item.subitems && item.subitems.length > 0) {
                for (const subitem of item.subitems) {
                    await processNavItem(subitem, level + 1, chapter.id);
                }
            }
        };

        for (const item of navigation.toc) {
            await processNavItem(item);
        }

        // Get spine items (actual reading order)
        const spine = await book.loaded.spine;
        let pageNumber = 1;

        for (const item of spine.items) {
            const section = await item.load(book.load.bind(book));
            const textContent = section.document.body.textContent || '';
            const htmlContent = section.document.body.innerHTML || '';

            // Find which chapter this spine item belongs to
            const chapter = chapters.find(ch => ch.href === item.href);

            pages.push({
                pageNumber: pageNumber++,
                chapterId: chapter?.id,
                contentHtml: htmlContent,
                contentText: textContent
            });
        }

        // Update chapter start/end pages
        for (let i = 0; i < chapters.length; i++) {
            const chapter = chapters[i];
            const chapterPages = pages.filter(p => p.chapterId === chapter.id);
            if (chapterPages.length > 0) {
                chapter.startPage = chapterPages[0].pageNumber;
                chapter.endPage = chapterPages[chapterPages.length - 1].pageNumber;
            }
        }

        return {
            metadata,
            chapters,
            pages,
            tableOfContents: navigation.toc,
            totalPages: pages.length,
            fileHash,
            originalFile: fileBuffer
        };
    }

    private async parsePdf(filePath: string, fileBuffer: Buffer, fileHash: string): Promise<ParsedBook> {
        const loadingTask = getDocument({
            data: fileBuffer,
            cMapUrl: 'node_modules/pdfjs-dist/cmaps/',
            cMapPacked: true,
        });

        const pdf = await loadingTask.promise;
        const metadata = await pdf.getMetadata();

        // Extract metadata
        const bookMetadata: BookMetadata = {
            title: metadata.info?.Title || 'Unknown PDF Title',
            author: metadata.info?.Author || 'Unknown Author',
            publisher: metadata.info?.Publisher,
            publishedDate: metadata.info?.CreationDate,
            language: metadata.info?.Language,
            format: 'pdf',
            pageCount: pdf.numPages
        };

        const pages: PageContent[] = [];
        const chapters: ChapterInfo[] = [];

        // Extract outline (table of contents)
        const outline = await pdf.getOutline();
        if (outline) {
            let chapterOrder = 0;
            const processOutlineItem = async (item: any, level: number = 0, parent?: string) => {
                const dest = item.dest;
                let pageIndex = 0;
                
                if (typeof dest === 'string') {
                    // Named destination - need to resolve
                    const destinations = await pdf.getDestinations();
                    const resolvedDest = destinations[dest];
                    if (resolvedDest) {
                        const ref = resolvedDest[0];
                        pageIndex = await pdf.getPageIndex(ref);
                    }
                } else if (Array.isArray(dest) && dest[0]) {
                    pageIndex = await pdf.getPageIndex(dest[0]);
                }

                const chapter: ChapterInfo = {
                    id: `chapter-${chapterOrder}`,
                    title: item.title,
                    href: `#page${pageIndex + 1}`,
                    order: chapterOrder++,
                    startPage: pageIndex + 1,
                    level,
                    parent
                };
                chapters.push(chapter);

                if (item.items && item.items.length > 0) {
                    for (const subitem of item.items) {
                        await processOutlineItem(subitem, level + 1, chapter.id);
                    }
                }
            };

            for (const item of outline) {
                await processOutlineItem(item);
            }
        }

        // Extract pages
        for (let i = 1; i <= pdf.numPages; i++) {
            const page = await pdf.getPage(i);
            const textContent = await page.getTextContent();
            const text = textContent.items.map((item: any) => item.str).join(' ');

            // Find which chapter this page belongs to
            const chapter = chapters
                .filter(ch => ch.startPage && ch.startPage <= i)
                .sort((a, b) => (b.startPage || 0) - (a.startPage || 0))[0];

            const viewport = page.getViewport({ scale: 1.0 });

            pages.push({
                pageNumber: i,
                chapterId: chapter?.id,
                contentText: text,
                width: viewport.width,
                height: viewport.height
            });
        }

        // Update chapter end pages
        for (let i = 0; i < chapters.length; i++) {
            const chapter = chapters[i];
            const nextChapter = chapters[i + 1];
            if (chapter.startPage) {
                chapter.endPage = nextChapter?.startPage ? nextChapter.startPage - 1 : pdf.numPages;
            }
        }

        return {
            metadata: bookMetadata,
            chapters,
            pages,
            tableOfContents: outline || [],
            totalPages: pdf.numPages,
            fileHash,
            originalFile: fileBuffer
        };
    }

    async extractCoverImage(book: ParsedBook): Promise<Buffer | null> {
        if (book.metadata.format === 'epub' && book.metadata.coverUrl) {
            // For EPUB, the cover URL is typically an internal reference
            // We need to extract it from the EPUB file
            try {
                const zip = await JSZip.loadAsync(book.originalFile);
                const coverPath = book.metadata.coverUrl.replace(/^\//, '');
                const coverFile = zip.file(coverPath);
                if (coverFile) {
                    return await coverFile.async('nodebuffer');
                }
            } catch (error) {
                console.error('Error extracting EPUB cover:', error);
            }
        } else if (book.metadata.format === 'pdf' && book.pages.length > 0) {
            // For PDF, we could render the first page as the cover
            // This would require additional implementation
            console.log('PDF cover extraction not yet implemented');
        }
        return null;
    }

    calculateFileHash(buffer: Buffer): string {
        return crypto.createHash('sha256').update(buffer).digest('hex');
    }
}

export default BookParser.getInstance();