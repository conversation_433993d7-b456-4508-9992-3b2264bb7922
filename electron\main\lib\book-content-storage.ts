import { Database } from 'sqlite3';
import { getDatabase } from '../database/database';
import { Book<PERSON>ars<PERSON>, ParsedBook, ChapterInfo, PageContent } from './book-parser';
import { runAsync, allAsync } from '../database/database-utils';

export interface StoredBookContent {
    id: number;
    bookId: number;
    format: string;
    fileHash: string;
    totalPages: number;
    totalChapters: number;
    tableOfContents: any;
    metadata: any;
    textContent?: string;
}

export class BookContentStorage {
    private db: Database;

    constructor() {
        this.db = getDatabase();
    }

    async storeBookContent(bookId: number, parsedBook: ParsedBook): Promise<void> {
        const db = this.db;
        
        try {
            // Begin transaction
            await runAsync(db, 'BEGIN TRANSACTION');

            // Store main book content
            await runAsync(db, `
                INSERT INTO book_content (
                    book_id, format, original_file, file_hash, 
                    total_pages, total_chapters, table_of_contents, 
                    metadata, text_content
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ON CONFLICT(book_id) DO UPDATE SET
                    format = excluded.format,
                    original_file = excluded.original_file,
                    file_hash = excluded.file_hash,
                    total_pages = excluded.total_pages,
                    total_chapters = excluded.total_chapters,
                    table_of_contents = excluded.table_of_contents,
                    metadata = excluded.metadata,
                    text_content = excluded.text_content,
                    updated_at = CURRENT_TIMESTAMP
            `, [
                bookId,
                parsedBook.metadata.format,
                parsedBook.originalFile,
                parsedBook.fileHash,
                parsedBook.totalPages,
                parsedBook.chapters.length,
                JSON.stringify(parsedBook.tableOfContents),
                JSON.stringify(parsedBook.metadata),
                parsedBook.pages.map(p => p.contentText).join('\n')
            ]);

            // Store chapters
            for (const chapter of parsedBook.chapters) {
                await this.storeChapter(bookId, chapter);
            }

            // Store pages
            for (const page of parsedBook.pages) {
                await this.storePage(bookId, page);
            }

            // Update book record with reading info
            await runAsync(db, `
                UPDATE books SET
                    has_content = 1,
                    book_file_format = ?,
                    total_pages = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `, [parsedBook.metadata.format, parsedBook.totalPages, bookId]);

            // Create search index entries
            await this.createSearchIndex(bookId, parsedBook);

            // Commit transaction
            await runAsync(db, 'COMMIT');
        } catch (error) {
            // Rollback on error
            await runAsync(db, 'ROLLBACK');
            throw error;
        }
    }

    private async storeChapter(bookId: number, chapter: ChapterInfo): Promise<void> {
        const db = this.db;
        
        await runAsync(db, `
            INSERT INTO book_chapters (
                book_id, chapter_number, title, start_page, 
                end_page, "order"
            ) VALUES (?, ?, ?, ?, ?, ?)
            ON CONFLICT(book_id, chapter_number) DO UPDATE SET
                title = excluded.title,
                start_page = excluded.start_page,
                end_page = excluded.end_page,
                "order" = excluded."order",
                updated_at = CURRENT_TIMESTAMP
        `, [
            bookId,
            chapter.order,
            chapter.title,
            chapter.startPage || null,
            chapter.endPage || null,
            chapter.order
        ]);
    }

    private async storePage(bookId: number, page: PageContent): Promise<void> {
        const db = this.db;
        
        // Get chapter ID if chapterId is provided
        let chapterDbId = null;
        if (page.chapterId) {
            const chapterResult = await allAsync(db, `
                SELECT id FROM book_chapters 
                WHERE book_id = ? AND "order" = ?
            `, [bookId, parseInt(page.chapterId.replace('chapter-', ''))]);
            
            if (chapterResult.length > 0) {
                chapterDbId = chapterResult[0].id;
            }
        }

        await runAsync(db, `
            INSERT INTO book_pages (
                book_id, chapter_id, page_number, 
                content_html, content_text, width, height
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ON CONFLICT(book_id, page_number) DO UPDATE SET
                chapter_id = excluded.chapter_id,
                content_html = excluded.content_html,
                content_text = excluded.content_text,
                width = excluded.width,
                height = excluded.height,
                updated_at = CURRENT_TIMESTAMP
        `, [
            bookId,
            chapterDbId,
            page.pageNumber,
            page.contentHtml || null,
            page.contentText,
            page.width || null,
            page.height || null
        ]);
    }

    private async createSearchIndex(bookId: number, parsedBook: ParsedBook): Promise<void> {
        const db = this.db;
        
        // Clear existing search index entries for this book
        await runAsync(db, `
            DELETE FROM book_search_index WHERE book_id = ?
        `, [bookId]);

        // Insert new search index entries
        for (const page of parsedBook.pages) {
            if (page.contentText && page.contentText.trim()) {
                await runAsync(db, `
                    INSERT INTO book_search_index (
                        book_id, page_number, chapter_id, content
                    ) VALUES (?, ?, ?, ?)
                `, [
                    bookId,
                    page.pageNumber,
                    page.chapterId || null,
                    page.contentText
                ]);
            }
        }
    }

    async getBookContent(bookId: number): Promise<StoredBookContent | null> {
        const db = this.db;
        
        const result = await allAsync(db, `
            SELECT * FROM book_content WHERE book_id = ?
        `, [bookId]);

        if (result.length === 0) {
            return null;
        }

        const content = result[0];
        return {
            id: content.id,
            bookId: content.book_id,
            format: content.format,
            fileHash: content.file_hash,
            totalPages: content.total_pages,
            totalChapters: content.total_chapters,
            tableOfContents: JSON.parse(content.table_of_contents || '[]'),
            metadata: JSON.parse(content.metadata || '{}'),
            textContent: content.text_content
        };
    }

    async getChapters(bookId: number): Promise<any[]> {
        const db = this.db;
        
        return await allAsync(db, `
            SELECT * FROM book_chapters 
            WHERE book_id = ? 
            ORDER BY "order"
        `, [bookId]);
    }

    async getPage(bookId: number, pageNumber: number): Promise<any | null> {
        const db = this.db;
        
        const result = await allAsync(db, `
            SELECT p.*, c.title as chapter_title
            FROM book_pages p
            LEFT JOIN book_chapters c ON p.chapter_id = c.id
            WHERE p.book_id = ? AND p.page_number = ?
        `, [bookId, pageNumber]);

        return result.length > 0 ? result[0] : null;
    }

    async getPageRange(bookId: number, startPage: number, endPage: number): Promise<any[]> {
        const db = this.db;
        
        return await allAsync(db, `
            SELECT p.*, c.title as chapter_title
            FROM book_pages p
            LEFT JOIN book_chapters c ON p.chapter_id = c.id
            WHERE p.book_id = ? AND p.page_number >= ? AND p.page_number <= ?
            ORDER BY p.page_number
        `, [bookId, startPage, endPage]);
    }

    async searchBookContent(bookId: number, query: string): Promise<any[]> {
        const db = this.db;
        
        return await allAsync(db, `
            SELECT 
                book_id,
                page_number,
                chapter_id,
                snippet(book_search_index, -1, '<mark>', '</mark>', '...', 32) as snippet
            FROM book_search_index
            WHERE book_id = ? AND book_search_index MATCH ?
            ORDER BY rank
            LIMIT 50
        `, [bookId, query]);
    }

    async deleteBookContent(bookId: number): Promise<void> {
        const db = this.db;
        
        try {
            await runAsync(db, 'BEGIN TRANSACTION');

            // Delete from all related tables
            await runAsync(db, 'DELETE FROM book_search_index WHERE book_id = ?', [bookId]);
            await runAsync(db, 'DELETE FROM book_pages WHERE book_id = ?', [bookId]);
            await runAsync(db, 'DELETE FROM book_chapters WHERE book_id = ?', [bookId]);
            await runAsync(db, 'DELETE FROM book_content WHERE book_id = ?', [bookId]);
            
            // Update book record
            await runAsync(db, `
                UPDATE books SET
                    has_content = 0,
                    reading_position = NULL,
                    reading_percentage = 0,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `, [bookId]);

            await runAsync(db, 'COMMIT');
        } catch (error) {
            await runAsync(db, 'ROLLBACK');
            throw error;
        }
    }
}

export default new BookContentStorage();