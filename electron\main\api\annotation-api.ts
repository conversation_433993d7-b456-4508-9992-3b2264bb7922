import { ipcMain } from 'electron';
import { getDatabase } from '../database/database';
import { allAsync, runAsync, getAsync } from '../database/database-utils';

export interface AnnotationData {
    bookId: number;
    type: 'highlight' | 'note' | 'underline' | 'comment';
    content?: string;
    selectedText: string;
    color?: string;
    pageNumber: number;
    chapterId?: number;
    startPosition?: string;
    endPosition?: string;
    pdfCoords?: string;
}

export interface BookmarkData {
    bookId: number;
    name?: string;
    description?: string;
    pageNumber: number;
    chapterId?: number;
    positionData?: string;
    color?: string;
}

export function registerAnnotationHandlers(): void {
    const db = getDatabase();

    // Create annotation
    ipcMain.handle('annotations:create', async (event, annotation: AnnotationData) => {
        try {
            const result = await runAsync(db, `
                INSERT INTO book_annotations (
                    book_id, type, content, selected_text, color,
                    page_number, chapter_id, start_position, end_position,
                    pdf_coords
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                annotation.bookId,
                annotation.type,
                annotation.content || null,
                annotation.selectedText,
                annotation.color || '#ffeb3b',
                annotation.pageNumber,
                annotation.chapterId || null,
                annotation.startPosition || null,
                annotation.endPosition || null,
                annotation.pdfCoords || null
            ]);

            // Update annotation count in books table
            await runAsync(db, `
                UPDATE books SET
                    annotation_count = (
                        SELECT COUNT(*) FROM book_annotations WHERE book_id = ?
                    ),
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `, [annotation.bookId, annotation.bookId]);

            return { success: true, annotationId: result.lastID };
        } catch (error) {
            console.error('Error creating annotation:', error);
            throw error;
        }
    });

    // Update annotation
    ipcMain.handle('annotations:update', async (event, annotationId: number, updates: Partial<AnnotationData>) => {
        try {
            const setClause: string[] = [];
            const values: any[] = [];

            if (updates.content !== undefined) {
                setClause.push('content = ?');
                values.push(updates.content);
            }
            if (updates.color !== undefined) {
                setClause.push('color = ?');
                values.push(updates.color);
            }
            if (updates.type !== undefined) {
                setClause.push('type = ?');
                values.push(updates.type);
            }

            if (setClause.length === 0) {
                return { success: true };
            }

            setClause.push('updated_at = CURRENT_TIMESTAMP');
            values.push(annotationId);

            await runAsync(db, `
                UPDATE book_annotations SET ${setClause.join(', ')}
                WHERE id = ?
            `, values);

            return { success: true };
        } catch (error) {
            console.error('Error updating annotation:', error);
            throw error;
        }
    });

    // Delete annotation
    ipcMain.handle('annotations:delete', async (event, annotationId: number) => {
        try {
            // Get book ID before deletion
            const annotation = await getAsync(db, `
                SELECT book_id FROM book_annotations WHERE id = ?
            `, [annotationId]);

            if (!annotation) {
                throw new Error('Annotation not found');
            }

            await runAsync(db, `
                DELETE FROM book_annotations WHERE id = ?
            `, [annotationId]);

            // Update annotation count
            await runAsync(db, `
                UPDATE books SET
                    annotation_count = (
                        SELECT COUNT(*) FROM book_annotations WHERE book_id = ?
                    ),
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `, [annotation.book_id, annotation.book_id]);

            return { success: true };
        } catch (error) {
            console.error('Error deleting annotation:', error);
            throw error;
        }
    });

    // Get annotations for a book
    ipcMain.handle('annotations:get-by-book', async (event, bookId: number) => {
        try {
            return await allAsync(db, `
                SELECT a.*, c.title as chapter_title
                FROM book_annotations a
                LEFT JOIN book_chapters c ON a.chapter_id = c.id
                WHERE a.book_id = ?
                ORDER BY a.page_number, a.created_at
            `, [bookId]);
        } catch (error) {
            console.error('Error getting annotations:', error);
            throw error;
        }
    });

    // Get annotations for a page
    ipcMain.handle('annotations:get-by-page', async (event, bookId: number, pageNumber: number) => {
        try {
            return await allAsync(db, `
                SELECT * FROM book_annotations
                WHERE book_id = ? AND page_number = ?
                ORDER BY created_at
            `, [bookId, pageNumber]);
        } catch (error) {
            console.error('Error getting page annotations:', error);
            throw error;
        }
    });

    // Convert annotation to note
    ipcMain.handle('annotations:convert-to-note', async (event, annotationId: number, folderId?: number) => {
        try {
            // Get annotation details
            const annotation = await getAsync(db, `
                SELECT a.*, b.title as book_title, c.title as chapter_title
                FROM book_annotations a
                JOIN books b ON a.book_id = b.id
                LEFT JOIN book_chapters c ON a.chapter_id = c.id
                WHERE a.id = ?
            `, [annotationId]);

            if (!annotation) {
                throw new Error('Annotation not found');
            }

            // Create note content
            const noteTitle = `${annotation.book_title} - Page ${annotation.page_number}`;
            const noteContent = `
## Annotation from "${annotation.book_title}"
${annotation.chapter_title ? `### Chapter: ${annotation.chapter_title}` : ''}
**Page:** ${annotation.page_number}

### Selected Text:
> ${annotation.selected_text}

${annotation.content ? `### Note:\n${annotation.content}` : ''}

---
*Created from ${annotation.type} on ${new Date().toLocaleDateString()}*
            `.trim();

            // Create note
            const noteResult = await runAsync(db, `
                INSERT INTO notes (
                    title, content, book_id, folder_id, 
                    annotation_id, reading_context
                ) VALUES (?, ?, ?, ?, ?, ?)
            `, [
                noteTitle,
                noteContent,
                annotation.book_id,
                folderId || null,
                annotationId,
                JSON.stringify({
                    pageNumber: annotation.page_number,
                    chapterTitle: annotation.chapter_title,
                    annotationType: annotation.type
                })
            ]);

            // Update annotation with note reference
            await runAsync(db, `
                UPDATE book_annotations SET
                    note_id = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `, [noteResult.lastID, annotationId]);

            return { success: true, noteId: noteResult.lastID };
        } catch (error) {
            console.error('Error converting annotation to note:', error);
            throw error;
        }
    });

    // Create bookmark
    ipcMain.handle('bookmarks:create', async (event, bookmark: BookmarkData) => {
        try {
            // Get current max order for this book
            const maxOrder = await getAsync(db, `
                SELECT MAX("order") as max_order
                FROM book_bookmarks
                WHERE book_id = ?
            `, [bookmark.bookId]);

            const order = (maxOrder?.max_order || 0) + 1;

            const result = await runAsync(db, `
                INSERT INTO book_bookmarks (
                    book_id, name, description, page_number,
                    chapter_id, position_data, color, "order"
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                bookmark.bookId,
                bookmark.name || `Bookmark ${order}`,
                bookmark.description || null,
                bookmark.pageNumber,
                bookmark.chapterId || null,
                bookmark.positionData || null,
                bookmark.color || '#4caf50',
                order
            ]);

            // Update bookmark count
            await runAsync(db, `
                UPDATE books SET
                    bookmark_count = (
                        SELECT COUNT(*) FROM book_bookmarks WHERE book_id = ?
                    ),
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `, [bookmark.bookId, bookmark.bookId]);

            return { success: true, bookmarkId: result.lastID };
        } catch (error) {
            console.error('Error creating bookmark:', error);
            throw error;
        }
    });

    // Update bookmark
    ipcMain.handle('bookmarks:update', async (event, bookmarkId: number, updates: Partial<BookmarkData>) => {
        try {
            const setClause: string[] = [];
            const values: any[] = [];

            if (updates.name !== undefined) {
                setClause.push('name = ?');
                values.push(updates.name);
            }
            if (updates.description !== undefined) {
                setClause.push('description = ?');
                values.push(updates.description);
            }
            if (updates.color !== undefined) {
                setClause.push('color = ?');
                values.push(updates.color);
            }

            if (setClause.length === 0) {
                return { success: true };
            }

            setClause.push('updated_at = CURRENT_TIMESTAMP');
            values.push(bookmarkId);

            await runAsync(db, `
                UPDATE book_bookmarks SET ${setClause.join(', ')}
                WHERE id = ?
            `, values);

            return { success: true };
        } catch (error) {
            console.error('Error updating bookmark:', error);
            throw error;
        }
    });

    // Delete bookmark
    ipcMain.handle('bookmarks:delete', async (event, bookmarkId: number) => {
        try {
            // Get book ID before deletion
            const bookmark = await getAsync(db, `
                SELECT book_id FROM book_bookmarks WHERE id = ?
            `, [bookmarkId]);

            if (!bookmark) {
                throw new Error('Bookmark not found');
            }

            await runAsync(db, `
                DELETE FROM book_bookmarks WHERE id = ?
            `, [bookmarkId]);

            // Update bookmark count
            await runAsync(db, `
                UPDATE books SET
                    bookmark_count = (
                        SELECT COUNT(*) FROM book_bookmarks WHERE book_id = ?
                    ),
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `, [bookmark.book_id, bookmark.book_id]);

            return { success: true };
        } catch (error) {
            console.error('Error deleting bookmark:', error);
            throw error;
        }
    });

    // Get bookmarks for a book
    ipcMain.handle('bookmarks:get-by-book', async (event, bookId: number) => {
        try {
            return await allAsync(db, `
                SELECT b.*, c.title as chapter_title
                FROM book_bookmarks b
                LEFT JOIN book_chapters c ON b.chapter_id = c.id
                WHERE b.book_id = ?
                ORDER BY b."order"
            `, [bookId]);
        } catch (error) {
            console.error('Error getting bookmarks:', error);
            throw error;
        }
    });

    // Reorder bookmarks
    ipcMain.handle('bookmarks:reorder', async (event, bookId: number, bookmarkIds: number[]) => {
        try {
            await runAsync(db, 'BEGIN TRANSACTION');

            for (let i = 0; i < bookmarkIds.length; i++) {
                await runAsync(db, `
                    UPDATE book_bookmarks SET
                        "order" = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ? AND book_id = ?
                `, [i + 1, bookmarkIds[i], bookId]);
            }

            await runAsync(db, 'COMMIT');
            return { success: true };
        } catch (error) {
            await runAsync(db, 'ROLLBACK');
            console.error('Error reordering bookmarks:', error);
            throw error;
        }
    });

    // Export annotations
    ipcMain.handle('annotations:export', async (event, bookId: number, format: 'json' | 'markdown' | 'txt' = 'markdown') => {
        try {
            const annotations = await allAsync(db, `
                SELECT a.*, b.title as book_title, b.author as book_author,
                       c.title as chapter_title
                FROM book_annotations a
                JOIN books b ON a.book_id = b.id
                LEFT JOIN book_chapters c ON a.chapter_id = c.id
                WHERE a.book_id = ?
                ORDER BY a.page_number, a.created_at
            `, [bookId]);

            if (format === 'json') {
                return { format: 'json', content: JSON.stringify(annotations, null, 2) };
            }

            let content = '';
            if (format === 'markdown') {
                content = `# Annotations for "${annotations[0]?.book_title || 'Unknown Book'}"\n`;
                content += `**Author:** ${annotations[0]?.book_author || 'Unknown Author'}\n\n`;
                
                for (const ann of annotations) {
                    content += `## Page ${ann.page_number}${ann.chapter_title ? ` - ${ann.chapter_title}` : ''}\n`;
                    content += `**Type:** ${ann.type}\n`;
                    content += `**Selected Text:**\n> ${ann.selected_text}\n`;
                    if (ann.content) {
                        content += `**Note:** ${ann.content}\n`;
                    }
                    content += `\n---\n\n`;
                }
            } else {
                // Plain text format
                content = `Annotations for "${annotations[0]?.book_title || 'Unknown Book'}"\n`;
                content += `Author: ${annotations[0]?.book_author || 'Unknown Author'}\n\n`;
                
                for (const ann of annotations) {
                    content += `Page ${ann.page_number}${ann.chapter_title ? ` - ${ann.chapter_title}` : ''}\n`;
                    content += `Type: ${ann.type}\n`;
                    content += `Selected Text: ${ann.selected_text}\n`;
                    if (ann.content) {
                        content += `Note: ${ann.content}\n`;
                    }
                    content += `\n---\n\n`;
                }
            }

            return { format, content };
        } catch (error) {
            console.error('Error exporting annotations:', error);
            throw error;
        }
    });
}