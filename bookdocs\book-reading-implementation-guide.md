# Book Reading System - Technical Implementation Guide

## Overview

This document provides a detailed technical implementation guide for integrating Foliate.js and building the complete book reading system in Noti. It covers library integration, parsing strategies, rendering techniques, and performance optimizations.

## Foliate.js Integration

### 1. Library Setup

#### 1.1 Installation

```bash
# Core Foliate.js library
npm install @johnfactotum/foliate-js

# Additional dependencies for different formats
npm install epub.js pdf.js
npm install jszip  # For EPUB handling
npm install pdfjs-dist  # For PDF rendering
```

#### 1.2 Configuration

```typescript
// electron/main/lib/book-parser.ts
import { Foliate } from '@johnfactotum/foliate-js'
import * as PDFJS from 'pdfjs-dist'

// Configure PDF.js worker
PDFJS.GlobalWorkerOptions.workerSrc = 
  path.join(__dirname, '../node_modules/pdfjs-dist/build/pdf.worker.js')

// Foliate configuration
const foliateConfig = {
  // Enable all format support
  formats: ['epub', 'pdf', 'mobi', 'azw3', 'fb2', 'cbz'],
  
  // Rendering options
  renderOptions: {
    width: 800,
    height: 600,
    scale: 1.5
  },
  
  // Parser options
  parserOptions: {
    extractImages: true,
    extractStyles: true,
    generateTOC: true
  }
}
```

### 2. Book Import & Parsing

#### 2.1 Main Parser Class

```typescript
// electron/main/lib/book-parser.ts
import crypto from 'crypto'
import { Book, BookContent } from '../types'

export class BookParser {
  private foliate: Foliate
  
  constructor() {
    this.foliate = new Foliate(foliateConfig)
  }
  
  async parseBook(filePath: string): Promise<ParsedBook> {
    const fileBuffer = await fs.promises.readFile(filePath)
    const fileHash = crypto.createHash('sha256').update(fileBuffer).digest('hex')
    
    // Detect format
    const format = await this.detectFormat(filePath, fileBuffer)
    
    // Parse based on format
    switch (format) {
      case 'epub':
        return await this.parseEPUB(fileBuffer, fileHash)
      case 'pdf':
        return await this.parsePDF(fileBuffer, fileHash)
      case 'mobi':
      case 'azw3':
        return await this.parseMOBI(fileBuffer, fileHash)
      // ... other formats
      default:
        throw new Error(`Unsupported format: ${format}`)
    }
  }
  
  private async detectFormat(
    filePath: string,
    buffer: Buffer
  ): Promise<BookFormat> {
    // Check file extension
    const ext = path.extname(filePath).toLowerCase().slice(1)
    if (['epub', 'pdf', 'mobi', 'azw3', 'fb2', 'cbz'].includes(ext)) {
      return ext as BookFormat
    }
    
    // Check file signature (magic bytes)
    const signature = buffer.slice(0, 8).toString('hex')
    
    if (signature.startsWith('504b0304')) return 'epub'  // ZIP format
    if (signature.startsWith('25504446')) return 'pdf'   // %PDF
    // ... other signatures
    
    throw new Error('Unknown file format')
  }
}
```

#### 2.2 EPUB Parser Implementation

```typescript
interface EPUBChapter {
  id: string
  title: string
  href: string
  content: string
  order: number
  level: number
}

private async parseEPUB(
  buffer: Buffer,
  fileHash: string
): Promise<ParsedBook> {
  const epub = await this.foliate.openEPUB(buffer)
  
  // Extract metadata
  const metadata = {
    title: epub.metadata.title || 'Untitled',
    author: epub.metadata.creator?.join(', ') || 'Unknown',
    publisher: epub.metadata.publisher,
    language: epub.metadata.language,
    description: epub.metadata.description,
    isbn: epub.metadata.identifier?.find(id => id.scheme === 'ISBN')?.value,
    publicationDate: epub.metadata.date
  }
  
  // Extract table of contents
  const toc = await this.extractEPUBTOC(epub)
  
  // Extract chapters
  const chapters: EPUBChapter[] = []
  const spine = epub.spine
  
  for (let i = 0; i < spine.length; i++) {
    const item = spine[i]
    const content = await epub.loadSpineItem(i)
    
    // Process content
    const processed = await this.processEPUBContent(content, epub)
    
    chapters.push({
      id: item.id,
      title: toc.find(t => t.href === item.href)?.title || `Chapter ${i + 1}`,
      href: item.href,
      content: processed.html,
      order: i,
      level: 0
    })
  }
  
  // Extract images
  const images = await this.extractEPUBImages(epub)
  
  // Generate search index
  const searchIndex = this.generateSearchIndex(chapters)
  
  return {
    metadata,
    format: 'epub',
    fileHash,
    chapters,
    images,
    toc,
    searchIndex,
    originalFile: buffer
  }
}

private async processEPUBContent(
  content: string,
  epub: any
): Promise<{ html: string, text: string }> {
  // Create a sandboxed DOM for processing
  const dom = new JSDOM(content)
  const document = dom.window.document
  
  // Process images - convert to data URLs or media references
  const images = document.querySelectorAll('img')
  for (const img of images) {
    const src = img.getAttribute('src')
    if (src) {
      const imageData = await epub.loadResource(src)
      const mediaUrl = await this.saveExtractedImage(imageData, src)
      img.setAttribute('src', mediaUrl)
    }
  }
  
  // Clean and sanitize HTML
  const cleanHTML = this.sanitizeHTML(document.body.innerHTML)
  
  // Extract plain text for search
  const plainText = document.body.textContent || ''
  
  return { html: cleanHTML, text: plainText }
}
```

#### 2.3 PDF Parser Implementation

```typescript
private async parsePDF(
  buffer: Buffer,
  fileHash: string
): Promise<ParsedBook> {
  const pdf = await PDFJS.getDocument({
    data: buffer,
    cMapUrl: '../node_modules/pdfjs-dist/cmaps/',
    cMapPacked: true
  }).promise
  
  const metadata = {
    title: pdf.info?.Title || 'Untitled PDF',
    author: pdf.info?.Author || 'Unknown',
    subject: pdf.info?.Subject,
    keywords: pdf.info?.Keywords,
    creator: pdf.info?.Creator,
    producer: pdf.info?.Producer,
    creationDate: pdf.info?.CreationDate,
    modificationDate: pdf.info?.ModDate
  }
  
  const pages: PDFPage[] = []
  const numPages = pdf.numPages
  
  // Process each page
  for (let i = 1; i <= numPages; i++) {
    const page = await pdf.getPage(i)
    
    // Extract text content
    const textContent = await page.getTextContent()
    const text = textContent.items
      .map((item: any) => item.str)
      .join(' ')
    
    // Render page to image
    const viewport = page.getViewport({ scale: 1.5 })
    const canvas = createCanvas(viewport.width, viewport.height)
    const context = canvas.getContext('2d')
    
    await page.render({
      canvasContext: context,
      viewport: viewport
    }).promise
    
    // Save rendered image
    const imageBuffer = canvas.toBuffer('png')
    const imagePath = await this.savePageImage(fileHash, i, imageBuffer)
    
    pages.push({
      pageNumber: i,
      text,
      imagePath,
      width: viewport.width,
      height: viewport.height
    })
  }
  
  // Extract outline (table of contents)
  const outline = await pdf.getOutline()
  const toc = outline ? this.parsePDFOutline(outline) : []
  
  return {
    metadata,
    format: 'pdf',
    fileHash,
    pages,
    toc,
    totalPages: numPages,
    originalFile: buffer
  }
}
```

### 3. Content Storage Strategy

#### 3.1 Database Storage Manager

```typescript
// electron/main/api/book-content-api.ts
export class BookContentManager {
  async importBook(filePath: string, bookId: number): Promise<void> {
    const parser = new BookParser()
    const parsed = await parser.parseBook(filePath)
    
    // Start transaction for atomic import
    await db.transaction(async (trx) => {
      // Store main content
      await this.storeBookContent(trx, bookId, parsed)
      
      // Store chapters/pages
      if (parsed.format === 'epub') {
        await this.storeChapters(trx, bookId, parsed.chapters)
      } else if (parsed.format === 'pdf') {
        await this.storePages(trx, bookId, parsed.pages)
      }
      
      // Store images
      await this.storeImages(trx, bookId, parsed.images)
      
      // Update book record
      await this.updateBookRecord(trx, bookId, {
        has_content: true,
        page_count: parsed.totalPages || parsed.chapters?.length,
        format: parsed.format
      })
    })
    
    // Build search index in background
    this.buildSearchIndex(bookId, parsed)
  }
  
  private async storeChapters(
    trx: Transaction,
    bookId: number,
    chapters: Chapter[]
  ): Promise<void> {
    for (const chapter of chapters) {
      // Split large chapters into chunks
      const chunks = this.splitIntoChunks(chapter.content)
      
      const chapterId = await trx('book_chapters').insert({
        book_id: bookId,
        chapter_number: chapter.order,
        title: chapter.title,
        content_html: chunks[0],  // First chunk
        content_text: chapter.text,
        order: chapter.order
      })
      
      // Store additional chunks if needed
      if (chunks.length > 1) {
        for (let i = 1; i < chunks.length; i++) {
          await trx('book_chapter_chunks').insert({
            chapter_id: chapterId,
            chunk_number: i,
            content: chunks[i]
          })
        }
      }
    }
  }
  
  private splitIntoChunks(content: string, maxSize = 1024 * 1024): string[] {
    if (content.length <= maxSize) return [content]
    
    const chunks: string[] = []
    let currentChunk = ''
    
    // Split by paragraph to avoid breaking mid-sentence
    const paragraphs = content.split('</p>')
    
    for (const para of paragraphs) {
      if (currentChunk.length + para.length > maxSize) {
        chunks.push(currentChunk)
        currentChunk = para + '</p>'
      } else {
        currentChunk += para + '</p>'
      }
    }
    
    if (currentChunk) chunks.push(currentChunk)
    return chunks
  }
}
```

### 4. Rendering System

#### 4.1 Content Renderer Factory

```typescript
// src/lib/book-renderer.ts
export class BookRenderer {
  private renderers: Map<BookFormat, FormatRenderer>
  
  constructor() {
    this.renderers = new Map([
      ['epub', new EPUBRenderer()],
      ['pdf', new PDFRenderer()],
      ['mobi', new MOBIRenderer()]
    ])
  }
  
  async renderContent(
    format: BookFormat,
    content: any,
    options: RenderOptions
  ): Promise<RenderedContent> {
    const renderer = this.renderers.get(format)
    if (!renderer) {
      throw new Error(`No renderer for format: ${format}`)
    }
    
    return renderer.render(content, options)
  }
}

// EPUB Renderer
class EPUBRenderer implements FormatRenderer {
  async render(
    content: ChapterContent,
    options: RenderOptions
  ): Promise<RenderedContent> {
    // Apply reading settings
    const styledContent = this.applyStyles(content.html, options)
    
    // Process annotations
    const annotatedContent = this.applyAnnotations(
      styledContent,
      options.annotations
    )
    
    // Paginate if needed
    if (options.viewMode === 'paginated') {
      return this.paginate(annotatedContent, options)
    }
    
    return {
      html: annotatedContent,
      pageBreaks: [],
      dimensions: null
    }
  }
  
  private applyStyles(html: string, options: RenderOptions): string {
    const styles = `
      <style>
        body {
          font-family: ${options.fontFamily};
          font-size: ${options.fontSize}px;
          line-height: ${options.lineHeight};
          color: ${options.textColor};
          background-color: ${options.backgroundColor};
          padding: ${options.padding}px;
          max-width: ${options.maxWidth}px;
          margin: 0 auto;
        }
        
        p { margin: 0 0 1em 0; }
        h1, h2, h3 { margin: 1.5em 0 0.5em 0; }
        
        /* Annotation highlights */
        .annotation-highlight {
          background-color: var(--highlight-color);
          cursor: pointer;
          position: relative;
        }
        
        .annotation-note {
          text-decoration: underline;
          text-decoration-style: wavy;
          text-decoration-color: var(--note-color);
        }
      </style>
    `
    
    return styles + html
  }
  
  private applyAnnotations(
    html: string,
    annotations: Annotation[]
  ): string {
    if (!annotations || annotations.length === 0) return html
    
    // Sort annotations by position to avoid overlaps
    const sorted = annotations.sort((a, b) => 
      a.start_position.offset - b.start_position.offset
    )
    
    let result = html
    let offset = 0
    
    for (const annotation of sorted) {
      const start = annotation.start_position.offset + offset
      const end = annotation.end_position.offset + offset
      
      const before = result.substring(0, start)
      const content = result.substring(start, end)
      const after = result.substring(end)
      
      const wrapped = this.wrapAnnotation(content, annotation)
      result = before + wrapped + after
      
      offset += wrapped.length - content.length
    }
    
    return result
  }
  
  private wrapAnnotation(text: string, annotation: Annotation): string {
    const classes = ['annotation', `annotation-${annotation.type}`]
    const styles = [`--highlight-color: ${annotation.color}`]
    
    return `<span 
      class="${classes.join(' ')}" 
      style="${styles.join('; ')}"
      data-annotation-id="${annotation.id}"
      data-annotation-type="${annotation.type}"
    >${text}</span>`
  }
}
```

#### 4.2 PDF Renderer

```typescript
class PDFRenderer implements FormatRenderer {
  async render(
    content: PageContent,
    options: RenderOptions
  ): Promise<RenderedContent> {
    // For PDF, we primarily work with images
    const pageImage = await this.loadPageImage(content.imagePath)
    
    // Create overlay for text selection and annotations
    const textLayer = this.createTextLayer(content.text, content.textPositions)
    const annotationLayer = this.createAnnotationLayer(options.annotations)
    
    return {
      html: `
        <div class="pdf-page" style="position: relative; width: ${content.width}px; height: ${content.height}px;">
          <img src="${pageImage}" class="pdf-page-image" />
          ${textLayer}
          ${annotationLayer}
        </div>
      `,
      pageBreaks: [content.height],
      dimensions: { width: content.width, height: content.height }
    }
  }
  
  private createTextLayer(text: string, positions: TextPosition[]): string {
    // Create invisible text layer for selection
    return `
      <div class="pdf-text-layer" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0;">
        ${positions.map(pos => `
          <span style="
            position: absolute;
            left: ${pos.x}px;
            top: ${pos.y}px;
            font-size: ${pos.fontSize}px;
            transform: scaleX(${pos.scaleX});
            opacity: 0;
            cursor: text;
          ">${pos.text}</span>
        `).join('')}
      </div>
    `
  }
}
```

### 5. Annotation System Implementation

#### 5.1 Text Selection Handler

```typescript
// src/composables/useTextSelection.ts
export function useTextSelection(bookId: number) {
  const selection = ref<TextSelection | null>(null)
  const isSelecting = ref(false)
  
  const handleSelection = () => {
    const sel = window.getSelection()
    if (!sel || sel.isCollapsed) {
      selection.value = null
      return
    }
    
    const range = sel.getRangeAt(0)
    const rect = range.getBoundingClientRect()
    
    // Get precise position data
    const position = getSelectionPosition(range)
    
    selection.value = {
      text: sel.toString(),
      range: range.cloneRange(),
      rect,
      position,
      startOffset: calculateOffset(range.startContainer, range.startOffset),
      endOffset: calculateOffset(range.endContainer, range.endOffset)
    }
    
    showAnnotationToolbar(rect)
  }
  
  const createHighlight = async (color: string) => {
    if (!selection.value) return
    
    const annotation = await window.db.annotations.create({
      book_id: bookId,
      type: 'highlight',
      selected_text: selection.value.text,
      color,
      start_position: selection.value.position.start,
      end_position: selection.value.position.end
    })
    
    // Apply highlight immediately
    highlightRange(selection.value.range, annotation)
    
    // Clear selection
    window.getSelection()?.removeAllRanges()
    selection.value = null
  }
  
  return {
    selection: readonly(selection),
    isSelecting: readonly(isSelecting),
    handleSelection,
    createHighlight,
    createNote,
    clearSelection
  }
}

// Position calculation utilities
function getSelectionPosition(range: Range): SelectionPosition {
  const start = getNodePosition(range.startContainer, range.startOffset)
  const end = getNodePosition(range.endContainer, range.endOffset)
  
  return {
    start: {
      node: start.path,
      offset: start.offset,
      paragraph: start.paragraph,
      sentence: start.sentence,
      word: start.word
    },
    end: {
      node: end.path,
      offset: end.offset,
      paragraph: end.paragraph,
      sentence: end.sentence,
      word: end.word
    }
  }
}
```

#### 5.2 Annotation Storage & Retrieval

```typescript
// electron/main/api/annotation-api.ts
export class AnnotationAPI {
  async createAnnotation(data: CreateAnnotationData): Promise<Annotation> {
    const annotation = await db('book_annotations').insert({
      book_id: data.book_id,
      type: data.type,
      content: data.content,
      selected_text: data.selected_text,
      color: data.color,
      page_number: data.page_number,
      chapter_id: data.chapter_id,
      start_position: JSON.stringify(data.start_position),
      end_position: JSON.stringify(data.end_position),
      pdf_coords: data.pdf_coords ? JSON.stringify(data.pdf_coords) : null
    })
    
    // Update annotation count
    await this.updateAnnotationCount(data.book_id)
    
    // Trigger sync if enabled
    if (await isAutoSyncEnabled()) {
      queueAnnotationSync(annotation.id)
    }
    
    return annotation
  }
  
  async getAnnotationsForPage(
    bookId: number,
    pageNumber: number
  ): Promise<Annotation[]> {
    return db('book_annotations')
      .where({ book_id: bookId, page_number: pageNumber })
      .orderBy('start_position')
  }
  
  async getAnnotationsForChapter(
    bookId: number,
    chapterId: number
  ): Promise<Annotation[]> {
    return db('book_annotations')
      .where({ book_id: bookId, chapter_id: chapterId })
      .orderBy('start_position')
  }
  
  async searchAnnotations(
    bookId: number,
    query: string
  ): Promise<Annotation[]> {
    return db('book_annotations')
      .where('book_id', bookId)
      .where(function() {
        this.where('selected_text', 'like', `%${query}%`)
          .orWhere('content', 'like', `%${query}%`)
      })
      .orderBy('created_at', 'desc')
  }
}
```

### 6. Search Implementation

#### 6.1 Full-Text Search

```typescript
// electron/main/api/book-search-api.ts
export class BookSearchAPI {
  async buildSearchIndex(bookId: number): Promise<void> {
    // Get all content
    const chapters = await db('book_chapters').where({ book_id: bookId })
    const pages = await db('book_pages').where({ book_id: bookId })
    
    // Clear existing index
    await db('book_search_index').where({ book_id: bookId }).delete()
    
    // Index chapters
    for (const chapter of chapters) {
      await db('book_search_index').insert({
        book_id: bookId,
        chapter_id: chapter.id,
        content: chapter.content_text
      })
    }
    
    // Index pages
    for (const page of pages) {
      await db('book_search_index').insert({
        book_id: bookId,
        page_number: page.page_number,
        content: page.content_text
      })
    }
  }
  
  async searchInBook(
    bookId: number,
    query: string,
    options: SearchOptions = {}
  ): Promise<SearchResult[]> {
    const results = await db.raw(`
      SELECT 
        book_id,
        page_number,
        chapter_id,
        snippet(book_search_index, -1, '<mark>', '</mark>', '...', 64) as snippet,
        rank
      FROM book_search_index
      WHERE book_id = ? AND content MATCH ?
      ORDER BY rank
      LIMIT ?
      OFFSET ?
    `, [
      bookId,
      query,
      options.limit || 50,
      options.offset || 0
    ])
    
    return results.map(r => ({
      bookId: r.book_id,
      pageNumber: r.page_number,
      chapterId: r.chapter_id,
      snippet: r.snippet,
      relevance: Math.abs(r.rank)
    }))
  }
}
```

### 7. Performance Optimizations

#### 7.1 Content Caching

```typescript
// src/lib/book-cache.ts
export class BookContentCache {
  private cache: LRUCache<string, CachedContent>
  private preloadQueue: Set<string>
  
  constructor(maxSize = 50 * 1024 * 1024) { // 50MB
    this.cache = new LRUCache({
      max: maxSize,
      length: (item) => item.size,
      dispose: (key, item) => this.cleanup(item)
    })
    
    this.preloadQueue = new Set()
  }
  
  async getChapter(bookId: number, chapterId: number): Promise<ChapterContent> {
    const key = `chapter:${bookId}:${chapterId}`
    
    let cached = this.cache.get(key)
    if (cached) return cached.content
    
    // Load from database
    const content = await window.db.books.getChapter(bookId, chapterId)
    
    // Cache it
    this.cache.set(key, {
      content,
      size: this.calculateSize(content)
    })
    
    // Preload adjacent chapters
    this.preloadAdjacent(bookId, chapterId)
    
    return content
  }
  
  private async preloadAdjacent(bookId: number, chapterId: number) {
    const adjacentIds = [
      chapterId - 1,
      chapterId + 1
    ].filter(id => id > 0)
    
    for (const id of adjacentIds) {
      const key = `chapter:${bookId}:${id}`
      if (!this.cache.has(key) && !this.preloadQueue.has(key)) {
        this.preloadQueue.add(key)
        
        // Preload in background
        setTimeout(() => {
          this.getChapter(bookId, id).then(() => {
            this.preloadQueue.delete(key)
          })
        }, 100)
      }
    }
  }
}
```

#### 7.2 Render Optimization

```typescript
// src/components/ReadingContent.vue
export default defineComponent({
  setup() {
    const contentCache = new BookContentCache()
    const renderQueue = new RenderQueue()
    
    // Virtual scrolling for continuous mode
    const virtualScroller = useVirtualScroll({
      itemHeight: 1000, // Approximate page height
      buffer: 2, // Render 2 pages above/below viewport
      onVisibleChange: (visible) => {
        // Load content for visible items
        visible.forEach(item => {
          renderQueue.enqueue(() => renderPage(item))
        })
      }
    })
    
    // Debounced rendering for smooth scrolling
    const debouncedRender = debounce((content: string) => {
      requestAnimationFrame(() => {
        applyContent(content)
      })
    }, 16) // 60 FPS
    
    return {
      virtualScroller,
      renderContent: debouncedRender
    }
  }
})
```

### 8. Error Handling & Recovery

```typescript
class BookImportErrorHandler {
  async handleImportError(
    error: Error,
    filePath: string
  ): Promise<ImportErrorResolution> {
    if (error instanceof UnsupportedFormatError) {
      return {
        message: `Format not supported: ${error.format}`,
        actions: ['cancel'],
        suggestion: 'Try converting to EPUB or PDF format'
      }
    }
    
    if (error instanceof CorruptedFileError) {
      return {
        message: 'File appears to be corrupted',
        actions: ['retry', 'repair', 'cancel'],
        suggestion: 'Try repairing the file or downloading it again'
      }
    }
    
    if (error instanceof FileTooLargeError) {
      return {
        message: `File too large: ${formatBytes(error.size)}`,
        actions: ['compress', 'split', 'cancel'],
        suggestion: `Maximum file size is ${formatBytes(MAX_FILE_SIZE)}`
      }
    }
    
    // Generic error
    return {
      message: error.message,
      actions: ['retry', 'cancel'],
      suggestion: 'Check the file and try again'
    }
  }
  
  async repairFile(filePath: string): Promise<string> {
    // Attempt to repair common issues
    const format = await detectFormat(filePath)
    
    switch (format) {
      case 'epub':
        return await this.repairEPUB(filePath)
      case 'pdf':
        return await this.repairPDF(filePath)
      default:
        throw new Error('Cannot repair this file format')
    }
  }
}
```

## Testing Strategy

### 1. Unit Tests

```typescript
// tests/book-parser.test.ts
describe('BookParser', () => {
  it('should parse EPUB files correctly', async () => {
    const parser = new BookParser()
    const result = await parser.parseBook('fixtures/test.epub')
    
    expect(result.format).toBe('epub')
    expect(result.metadata.title).toBe('Test Book')
    expect(result.chapters).toHaveLength(10)
  })
  
  it('should handle corrupted files gracefully', async () => {
    const parser = new BookParser()
    await expect(
      parser.parseBook('fixtures/corrupted.epub')
    ).rejects.toThrow(CorruptedFileError)
  })
})
```

### 2. Integration Tests

```typescript
// tests/book-import-flow.test.ts
describe('Book Import Flow', () => {
  it('should import book and create all database records', async () => {
    const bookId = await createTestBook()
    await importBook('fixtures/complete.epub', bookId)
    
    // Verify content
    const content = await db('book_content').where({ book_id: bookId }).first()
    expect(content).toBeDefined()
    expect(content.total_chapters).toBe(10)
    
    // Verify chapters
    const chapters = await db('book_chapters').where({ book_id: bookId })
    expect(chapters).toHaveLength(10)
    
    // Verify search index
    const index = await db('book_search_index').where({ book_id: bookId })
    expect(index.length).toBeGreaterThan(0)
  })
})
```

## Migration Plan

### Phase 1: Foundation (Week 1-2)
1. Set up Foliate.js integration
2. Implement basic book parser
3. Create database tables
4. Basic import functionality

### Phase 2: Core Reading (Week 3-4)
1. Implement content renderers
2. Basic navigation
3. Reading progress tracking
4. Page caching

### Phase 3: Annotations (Week 5-6)
1. Text selection handling
2. Annotation creation/storage
3. Annotation rendering
4. Bookmark system

### Phase 4: Advanced Features (Week 7-8)
1. Full-text search
2. Table of contents
3. Multiple format support
4. Export functionality

### Phase 5: Sync & Polish (Week 9-10)
1. Sync system integration
2. Performance optimization
3. Error handling
4. UI polish
