# Book Reading System - Sync System Integration

## Overview

This document details how the book reading system will integrate with <PERSON><PERSON>'s existing sync infrastructure. The sync system must handle large book files, annotations, bookmarks, and reading progress across devices.

## Sync Architecture Modifications

### 1. Manifest Structure Updates

#### Extended ManifestItem for Books

```typescript
interface BookManifestItem extends ManifestItem {
  type: 'book'
  metadata: {
    // Existing book metadata
    title: string
    author: string
    isbn?: string
    
    // New reading-specific metadata
    hasContent: boolean
    contentHash?: string  // Hash of book file
    format?: BookFormat
    totalPages?: number
    totalChapters?: number
    fileSize?: number  // In bytes
    
    // Reading progress
    lastReadAt?: string
    readingPosition?: string // Changed to string to match DB TEXT type for JSON
    readingPercentage?: number
    totalReadingTime?: number
    
    // Counts for quick reference
    bookmarkCount?: number
    annotationCount?: number
  }
}
```

#### New Manifest Item Types

```typescript
interface AnnotationManifestItem extends ManifestItem {
  type: 'annotation'
  relationships: {
    bookId: string
    noteId?: string  // If linked to a note
  }
  metadata: {
    annotationType: 'highlight' | 'note' | 'underline' | 'comment'
    color?: string
    selectedText?: string
    pageNumber?: number
    chapterId?: string
    startPosition?: string // Changed to string to match DB TEXT type for JSON
    endPosition?: string   // Changed to string to match DB TEXT type for JSON
    pdfCoords?: string     // Changed to string to match DB TEXT type for JSON
    content?: string       // The annotation text/comment
  }
}

interface BookmarkManifestItem extends ManifestItem {
  type: 'bookmark'
  relationships: {
    bookId: string
  }
  metadata: {
    name?: string
    description?: string
    pageNumber?: number
    chapterId?: string
    positionData?: string // Changed to string to match DB TEXT type for JSON
    color?: string
    order?: number
  }
}

interface ReadingProgressManifestItem extends ManifestItem {
  type: 'reading_progress'
  relationships: {
    bookId: string
  }
  metadata: {
    lastReadAt: string
    currentCfi?: string // For EPUB
    pdfPosition?: { page: number, x: number, y: number } // For PDF
    progressPercentage: number
    totalReadingTime: number
  }
}

interface ReadingSettingsManifestItem extends ManifestItem {
  type: 'reading_settings'
  relationships: {
    bookId: string
  }
  metadata: {
    fontSize: number
    fontFamily: string
    lineHeight: number
    theme: string
    pageWidth: number
    margin: number
    flow: string
    maxColumnCount: number
  }
}
```

### 2. File Structure in Sync Directory

```
sync-directory/
├── .sync-manifest.json
├── Books/
│   ├── Book Title 1/
│   │   ├── .book-meta.json          # Enhanced with reading data
│   │   ├── content/
│   │   │   ├── original.epub        # Original file
│   │   │   ├── chapters/            # Parsed chapters
│   │   │   │   ├── chapter-1.json
│   │   │   │   ├── chapter-2.json
│   │   │   │   └── ...
│   │   │   ├── pages/               # For PDF format
│   │   │   │   ├── page-1.json
│   │   │   │   ├── page-1.png      # Rendered page image
│   │   │   │   └── ...
│   │   │   └── search-index.json    # Pre-built search index
│   │   ├── media/                   # Extracted images
│   │   │   ├── image-1.jpg
│   │   │   └── ...
│   │   ├── annotations/             # All annotations
│   │   │   ├── annotation-1.json
│   │   │   ├── annotation-2.json
│   │   │   └── ...
│   │   ├── bookmarks/               # All bookmarks
│   │   │   ├── bookmark-1.json
│   │   │   └── ...
│   │   └── reading-progress.json    # Current reading state
│   └── Book Title 2/
│       └── ...
└── ...
```

### 3. Sync Strategy for Large Files

#### 3.1 Incremental Content Sync

```typescript
interface BookContentSync {
  // Check if content needs syncing
  async needsContentSync(book: Book): Promise<boolean> {
    const localHash = await calculateBookHash(book.id)
    const remoteHash = await getRemoteBookHash(book.id)
    return localHash !== remoteHash
  }
  
  // Sync only changed chapters/pages
  async syncIncrementalContent(book: Book): Promise<void> {
    const changes = await detectContentChanges(book)
    
    for (const change of changes) {
      if (change.type === 'chapter') {
        await syncChapter(book.id, change.chapterId)
      } else if (change.type === 'page') {
        await syncPage(book.id, change.pageNumber)
      }
    }
  }
}
```

#### 3.2 Chunked Upload/Download

```typescript
const CHUNK_SIZE = 5 * 1024 * 1024  // 5MB chunks

async function uploadLargeBookFile(
  filePath: string,
  remotePath: string
): Promise<void> {
  const fileSize = await getFileSize(filePath)
  const totalChunks = Math.ceil(fileSize / CHUNK_SIZE)
  
  for (let i = 0; i < totalChunks; i++) {
    const chunk = await readFileChunk(filePath, i * CHUNK_SIZE, CHUNK_SIZE)
    const chunkPath = `${remotePath}.chunk${i}`
    
    await uploadChunk(chunkPath, chunk)
    
    // Update progress
    emitSyncProgress({
      phase: 'exporting',
      current: `Uploading book chunk ${i + 1}/${totalChunks}`,
      progress: ((i + 1) / totalChunks) * 100
    })
  }
  
  // Combine chunks on completion
  await combineChunks(remotePath, totalChunks)
}
```

### 4. Annotation & Bookmark Sync

#### 4.1 Sync Flow

```typescript
class AnnotationSyncManager {
  async syncAnnotations(bookId: string): Promise<void> {
    // Get local annotations
    const localAnnotations = await getBookAnnotations(bookId)
    
    // Get remote annotations
    const remoteAnnotations = await getRemoteAnnotations(bookId)
    
    // Detect changes
    const changes = this.detectAnnotationChanges(
      localAnnotations,
      remoteAnnotations
    )
    
    // Apply changes
    for (const change of changes.toImport) {
      await this.importAnnotation(change)
    }
    
    for (const change of changes.toExport) {
      await this.exportAnnotation(change)
    }
    
    // Handle conflicts
    for (const conflict of changes.conflicts) {
      await this.resolveAnnotationConflict(conflict)
    }
  }
  
  private detectAnnotationChanges(
    local: Annotation[],
    remote: AnnotationManifestItem[]
  ): AnnotationChanges {
    // Compare by ID and modification time
    // Handle position-based matching for same text
    // Detect moved annotations
  }
}
```

#### 4.2 Conflict Resolution

For annotations on the same text/position:

```typescript
interface AnnotationConflict {
  local: Annotation
  remote: AnnotationManifestItem
  resolution: AnnotationConflictResolution
}

enum AnnotationConflictResolution {
  KEEP_LOCAL = 'keep_local',
  KEEP_REMOTE = 'keep_remote',
  KEEP_BOTH = 'keep_both',  // Creates duplicate
  MERGE = 'merge'  // Combine notes
}

// Auto-resolution rules
function autoResolveAnnotationConflict(conflict: AnnotationConflict): AnnotationConflictResolution {
  // If same user, keep newest
  if (conflict.local.updated_at > conflict.remote.modified) {
    return AnnotationConflictResolution.KEEP_LOCAL
  }
  
  // If different types, keep both
  if (conflict.local.type !== conflict.remote.metadata.annotationType) {
    return AnnotationConflictResolution.KEEP_BOTH
  }
  
  // If notes differ significantly, keep both
  if (areNotesDifferent(conflict.local.content, conflict.remote.metadata.content)) {
    return AnnotationConflictResolution.KEEP_BOTH
  }
  
  return AnnotationConflictResolution.KEEP_REMOTE
}
```

### 5. Reading Progress Sync

#### 5.1 Progress Data Structure

```typescript
interface ReadingProgress {
  bookId: string
  lastReadAt: string
  position: {
    page?: number
    chapter?: string
    percentage: number
    // Precise position for different formats
    epubCFI?: string  // EPUB
    pdfPosition?: { page: number, x: number, y: number }  // PDF
  }
  statistics: {
    totalReadingTime: number  // seconds
    sessionsCount: number
    averageSessionLength: number
    pagesRead: number
    readingSpeed: number  // pages per minute
  }
  // Reading session history (last 10)
  recentSessions: ReadingSession[]
}
```

#### 5.2 Progress Sync Strategy

```typescript
class ReadingProgressSync {
  // Sync on these events:
  // - Page turn
  // - Session end
  // - App close
  // - Manual sync
  
  async syncProgress(book: Book): Promise<void> {
    const localProgress = await getLocalProgress(book.id)
    const remoteProgress = await getRemoteProgress(book.id)
    
    if (!remoteProgress || localProgress.lastReadAt > remoteProgress.lastReadAt) {
      // Local is newer
      await this.uploadProgress(book.id, localProgress)
    } else if (localProgress.lastReadAt < remoteProgress.lastReadAt) {
      // Remote is newer
      await this.applyRemoteProgress(book.id, remoteProgress)
    } else {
      // Same timestamp - merge statistics
      const merged = this.mergeProgressStats(localProgress, remoteProgress)
      await this.saveProgress(book.id, merged)
    }
  }
  
  private mergeProgressStats(
    local: ReadingProgress,
    remote: ReadingProgress
  ): ReadingProgress {
    return {
      ...local,
      statistics: {
        totalReadingTime: Math.max(
          local.statistics.totalReadingTime,
          remote.statistics.totalReadingTime
        ),
        sessionsCount: Math.max(
          local.statistics.sessionsCount,
          remote.statistics.sessionsCount
        ),
        // ... merge other stats
      }
    }
  }
}
```

### 6. Optimizations

#### 6.1 Differential Sync

Only sync changed content:

```typescript
interface ContentDiff {
  bookId: string
  changes: Array<{
    type: 'annotation' | 'bookmark' | 'progress' | 'content'
    operation: 'create' | 'update' | 'delete'
    data: any
  }>
  timestamp: string
}

// Track changes since last sync
class ChangeTracker {
  private changes: Map<string, ContentDiff> = new Map()
  
  trackChange(bookId: string, change: any) {
    if (!this.changes.has(bookId)) {
      this.changes.set(bookId, {
        bookId,
        changes: [],
        timestamp: new Date().toISOString()
      })
    }
    
    this.changes.get(bookId)!.changes.push(change)
  }
  
  getChangesSince(bookId: string, timestamp: string): ContentDiff | null {
    const diff = this.changes.get(bookId)
    if (!diff) return null
    
    return {
      ...diff,
      changes: diff.changes.filter(c => c.timestamp > timestamp)
    }
  }
}
```

#### 6.2 Background Sync

```typescript
class BackgroundBookSync {
  private syncQueue: Set<string> = new Set()
  private syncing: boolean = false
  
  // Queue book for background sync
  queueSync(bookId: string) {
    this.syncQueue.add(bookId)
    this.processSyncQueue()
  }
  
  private async processSyncQueue() {
    if (this.syncing || this.syncQueue.size === 0) return
    
    this.syncing = true
    const bookId = this.syncQueue.values().next().value
    this.syncQueue.delete(bookId)
    
    try {
      // Sync in priority order
      await this.syncReadingProgress(bookId)  // Fast
      await this.syncAnnotations(bookId)      // Medium
      await this.syncBookContent(bookId)      // Slow
    } catch (error) {
      console.error(`Background sync failed for book ${bookId}:`, error)
      // Re-queue on failure
      this.syncQueue.add(bookId)
    }
    
    this.syncing = false
    
    // Process next item
    if (this.syncQueue.size > 0) {
      setTimeout(() => this.processSyncQueue(), 1000)
    }
  }
}
```

### 7. Auto-Sync Triggers

```typescript
interface BookAutoSyncTriggers {
  // Immediate sync
  onAnnotationCreated: (annotation: Annotation) => void
  onBookmarkCreated: (bookmark: Bookmark) => void
  onReadingSessionEnd: (session: ReadingSession) => void
  
  // Debounced sync (5 seconds)
  onPageChanged: (page: number) => void
  onProgressUpdated: (progress: ReadingProgress) => void
  
  // Periodic sync (every 5 minutes)
  onReadingActive: () => void
}

// Implementation
const bookAutoSync = {
  init(book: Book) {
    // Immediate triggers
    eventBus.on('annotation:created', (annotation) => {
      if (annotation.book_id === book.id) {
        syncManager.syncAnnotation(annotation)
      }
    })
    
    // Debounced triggers
    const debouncedProgressSync = debounce(() => {
      syncManager.syncReadingProgress(book.id)
    }, 5000)
    
    eventBus.on('page:changed', (bookId, page) => {
      if (bookId === book.id) {
        debouncedProgressSync()
      }
    })
    
    // Periodic sync
    setInterval(() => {
      if (isBookActive(book.id)) {
        syncManager.syncBook(book.id, { lightweight: true })
      }
    }, 5 * 60 * 1000)
  }
}
```

### 8. Sync API Endpoints

```typescript
// New IPC handlers for book sync
interface BookSyncAPI {
  // Manual sync
  'sync:book': (bookId: number) => Promise<SyncResult>
  'sync:book-content': (bookId: number) => Promise<void>
  'sync:annotations': (bookId: number) => Promise<void>
  'sync:reading-progress': (bookId: number) => Promise<void>
  
  // Sync status
  'sync:get-book-status': (bookId: number) => Promise<BookSyncStatus>
  'sync:get-pending-changes': (bookId: number) => Promise<ContentDiff>
  
  // Conflict resolution
  'sync:resolve-annotation-conflict': (
    conflictId: string,
    resolution: AnnotationConflictResolution
  ) => Promise<void>
}
```

### 9. Error Handling

```typescript
class BookSyncErrorHandler {
  handleSyncError(error: Error, context: SyncContext): SyncErrorResolution {
    if (error instanceof NetworkError) {
      return { retry: true, delay: 5000 }
    }
    
    if (error instanceof FileTooLargeError) {
      return { 
        retry: false,
        action: 'compress',
        message: 'Book file too large for sync. Compressing...'
      }
    }
    
    if (error instanceof CorruptedFileError) {
      return {
        retry: false,
        action: 'reimport',
        message: 'Book file corrupted. Please reimport.'
      }
    }
    
    return { retry: false, action: 'skip' }
  }
}
```

## Implementation Phases

### Phase 1: Basic Structure
1. Update manifest types
2. Create book content sync directories
3. Implement progress sync

### Phase 2: Content Sync
1. Chunked file upload/download
2. Incremental chapter/page sync
3. Content hash verification

### Phase 3: Annotation Sync
1. Annotation export/import
2. Conflict detection
3. Auto-resolution rules

### Phase 4: Optimization
1. Background sync queue
2. Differential sync
3. Compression for large files
4. Bandwidth management
