# Book Reading: Remediation and Next Steps Plan

## 1. Overview

This document provides a detailed, actionable plan to complete the book reading feature. It replaces the previous `BOOK_READING_IMPLEMENTATION_PROGRESS.md` and serves as the active roadmap.

The implementation is currently **70% complete**. The backend is production-quality, but the frontend is non-functional. This plan addresses critical architectural flaws before proceeding with feature implementation.

## 2. Phase 0: Foundational Remediation (Highest Priority)

These tasks must be completed before any new feature work begins to ensure the long-term stability, security, and performance of the application.

### Epic 0.1: Refactor Content Storage (Critical Architectural Fix)

**Problem**: Storing book files as BLOBs in the database is not scalable and risks corrupting the entire user database.
**Solution**: Migrate to a filesystem-based storage strategy.

**Tasks**:
1.  **Schema Change**:
    -   In `electron/main/database/database.ts`, add a `content_file_path TEXT` column to the `book_content` table.
    -   The `original_file BLOB` column should be marked for deprecation and removal after migration.
2.  **Migration Script**:
    -   Create a one-time migration script in `electron/main/database/migrations/`.
    -   This script will iterate through all entries in `book_content`. For each entry, it will:
        a. Extract the BLOB data into a file.
        b. Save the file to a secure, well-known location within the Electron app's user data directory (e.g., `userData/book_content/<book_id>/<file_hash>.epub`).
        c. Update the `content_file_path` column with the new file's relative path.
3.  **API Updates**:
    -   Modify `electron/main/lib/book-parser.ts` to read files from the filesystem, not a database buffer.
    -   Modify `electron/main/api/book-content-api.ts` and `electron/main/lib/book-content-storage.ts` to save imported book files to the filesystem and store the path in the database, instead of the BLOB.

### Epic 0.2: Implement Security Hardening

**Problem**: The current EPUB parser does not sanitize HTML, posing an XSS risk.
**Solution**: Integrate a sanitization library.

**Tasks**:
1.  **Add Sanitizer**:
    -   `npm install dompurify` and `@types/dompurify`.
2.  **Update Parser**:
    -   In `electron/main/lib/book-parser.ts`, within the `parseEPUB` method, after processing the HTML content of a chapter, pass it through `DOMPurify.sanitize()`.
3.  **Add Validation**:
    -   Implement file size limits and strict file type validation during the import process in `book-content-api.ts` to prevent users from importing excessively large or malicious files.

## 3. Phase 1: Minimum Viable Product (MVP)

**Goal**: A user can import a book, open it, and read it page by page.

### Epic 1.1: Implement File Import

**Problem**: The frontend cannot import files.
**Solution**: Use Electron's native dialogs.

**Tasks**:
1.  **IPC Handler**: Create a new IPC handler in `ipc-handlers.ts`, e.g., `handle-file-open`, that calls `dialog.showOpenDialog` and returns the selected file path(s).
2.  **UI Integration**: In `src/components/BookReader.vue`, replace the placeholder file input with a button that invokes the new IPC handler.
3.  **Connect Logic**: On receiving a file path from the handler, call the existing `window.db.books.importFile(bookId, filePath)` API.

### Epic 1.2: Implement Core Rendering

**Problem**: The reader displays raw text, not a rendered book.
**Solution**: Integrate a rendering library for one format.

**Tasks**:
1.  **Library Installation**: `npm install pdfjs-dist`.
2.  **Component Creation**: Following `book-reading-ui-architecture.md`, create a `ReadingContent.vue` component.
3.  **PDF.js Integration**: Implement the PDF rendering logic as described in `book-reading-implementation-guide.md`. This involves rendering the PDF page onto a `<canvas>` element.
4.  **Update BookReader**: Use the new `ReadingContent.vue` component to display the content.

### Epic 1.3: Centralize State

**Problem**: UI state is local and cannot be shared between components.
**Solution**: Implement a Pinia store.

**Tasks**:
1.  **Create Store**: Create `src/stores/bookReaderStore.ts`.
2.  **Define State**: Add state for `currentBook`, `currentPage`, `totalPages`, `isLoading`, etc.
3.  **Refactor Component**: Move all relevant state from `BookReader.vue` into the Pinia store. The component should now read from and dispatch actions to the store.

## 4. Phase 2: Core Features

**Goal**: A user can actively engage with the book by creating annotations.

### Epic 2.1: Annotation Creation

**Tasks**:
1.  **Text Selection Composable**: Create `src/composables/useTextSelection.ts` to handle user text selection within the `ReadingContent` component.
2.  **Annotation Toolbar**: Create the floating `AnnotationToolbar.vue` component that appears on text selection.
3.  **Create Highlights**: Wire the toolbar to the `window.db.annotations.create` API call to save highlights to the database and update the Pinia store.

### Epic 2.2: Annotation Display & UI Componentization

**Tasks**:
1.  **Annotation Rendering**: Implement logic within `ReadingContent.vue` to draw highlights on the page based on the annotation data in the store.
2.  **Component Breakdown**: Break down the monolithic `BookReader.vue` into the sub-components defined in `book-reading-ui-architecture.md` (`ReadingToolbar`, `AnnotationsSidebar`, etc.).
3.  **Annotation List**: Implement the `AnnotationsSidebar.vue` to display a list of all annotations for the current book, fetched from the Pinia store.

---
