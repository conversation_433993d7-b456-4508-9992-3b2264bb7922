# Book Reading Implementation Todo List

## 🚀 Current Status Summary

### ✅ Completed Major Milestones
1. **Database Schema** - All book reading tables created and indexed
2. **Book Parser** - EPUB and PDF parsing implemented with content storage
3. **Backend APIs** - Complete API layer for content, annotations, bookmarks, and search
4. **IPC Integration** - All handlers registered and connected
5. **Frontend Bridge** - APIs exposed to renderer process
6. **Basic Reader UI** - BookReader component with page navigation and progress tracking

### 🎯 Next Priority Tasks
1. **Create bookReaderStore.ts** - Centralized state management (Phase 7)
2. **Implement file dialog** - Proper book import through Electron dialog
3. **Integrate PDF.js** - Render PDF content properly (Phase 5)
4. **Add annotation UI** - Text selection and highlight creation (Phase 8)
5. **Keyboard shortcuts** - Navigation and quick actions

### ⚠️ Current Limitations
- File import through drag-and-drop not working (browser security)
- No actual PDF/EPUB rendering (just raw text)
- No annotation creation UI
- No bookmarks UI
- No chapter navigation

## 📖 Phase 1: Database Schema Extensions

### Completed ✓
- [x] Create database migration script for new book reading tables
- [x] Add new columns to existing books table for reading progress (has_content, last_read_at, reading_position, reading_percentage, total_reading_time, bookmark_count, annotation_count, book_file_path, book_file_format, file_size)
- [x] Create book_content table for storing parsed book data
- [x] Create book_chapters and book_pages tables
- [x] Create book_bookmarks and book_annotations tables
- [x] Create book_reading_sessions and book_reading_settings tables
- [x] Create book_search_index virtual table using FTS5
- [x] Add indexes for all new tables (book_content, chapters, pages, bookmarks, annotations, reading_sessions, reading_settings)
- [x] Add migration code to handle database updates in handleDatabaseMigrations function
- [x] Add columns to notes table for annotation integration (annotation_id, reading_context)

## 🔧 Phase 2: Book Parser Integration

### Completed ✓
- [x] Install EPUB and PDF parsing dependencies (epubjs, pdfjs-dist, jszip, crypto-js)
- [x] Create book parser module in electron/main/lib/book-parser.ts
- [x] Implement EPUB parser with chapter extraction using epubjs
- [x] Implement PDF parser with page extraction using pdfjs-dist
- [x] Create book content storage manager (book-content-storage.ts)
- [x] Create database utility functions (database-utils.ts)
- [x] Implement BookParser class with singleton pattern
- [x] Add parseBook method supporting EPUB and PDF formats
- [x] Create interfaces for BookMetadata, ChapterInfo, PageContent, and ParsedBook
- [x] Implement file hash calculation for duplicate detection
- [x] Add cover image extraction method (implementation pending)

### Tasks
- [ ] Complete cover image extraction implementation
- [ ] Add support for MOBI/AZW3 formats
- [ ] Add support for FB2 format
- [ ] Add support for CBZ (comic book) format
- [ ] Add file validation and error handling
- [ ] Implement progress callback for large file parsing

## 🔌 Phase 3: API Layer Implementation

### Completed ✓
- [x] Create book-content-api.ts for book content operations
  - [x] Implement book-content:parse handler for parsing and storing book files
  - [x] Implement book-content:get-metadata handler
  - [x] Implement book-content:get-chapters handler
  - [x] Implement book-content:get-page handler for single page retrieval
  - [x] Implement book-content:get-page-range handler
  - [x] Implement book-content:update-progress handler for reading progress
  - [x] Implement book-content:start-session and end-session handlers
  - [x] Implement book-content:get-sessions handler
  - [x] Implement book-content:search handler for in-book search
  - [x] Implement book-content:delete handler
  - [x] Implement book-content:has-content checker
  - [x] Implement book-content:import-file handler
- [x] Create annotation-api.ts for annotations/bookmarks management
  - [x] Implement annotations:create handler
  - [x] Implement annotations:update handler
  - [x] Implement annotations:delete handler
  - [x] Implement annotations:get-by-book handler
  - [x] Implement annotations:get-by-page handler
  - [x] Implement annotations:convert-to-note handler
  - [x] Implement bookmarks:create handler
  - [x] Implement bookmarks:update handler
  - [x] Implement bookmarks:delete handler
  - [x] Implement bookmarks:get-by-book handler
  - [x] Implement bookmarks:reorder handler
  - [x] Implement annotations:export handler (json, markdown, txt formats)
- [x] Create book-search-api.ts for full-text search functionality
  - [x] Implement book-search:search-in-book handler with content and annotation search
  - [x] Implement book-search:search-all-books handler
  - [x] Implement book-search:get-suggestions handler
  - [x] Implement book-search:rebuild-index handler
  - [x] Implement book-search:clear-index handler
  - [x] Implement book-search:get-stats handler

### Completed ✓
- [x] Add new IPC handlers for book reading in ipc-handlers.ts
  - [x] Import book-content-api, annotation-api, and book-search-api
  - [x] Create registerBookReadingHandlers() function
  - [x] Register all book reading handlers in initializeIpcHandlers()
- [x] Update frontend API bridge in preload/api-bridge.ts
  - [x] Add BookContentAPI, AnnotationsAPI, BookmarksAPI, BookSearchAPI interfaces
  - [x] Implement all API methods in dbApi object
  - [x] Update Window interface to expose new APIs

### Tasks
- [ ] Extend books-api.ts with reading functionality
- [ ] Create reading-settings-api.ts for managing reading preferences
- [ ] Add error handling and validation to all API endpoints

## 🎨 Phase 4: UI Components - BookDetailsModal Enhancement

### Tasks
- [ ] Add 'Read' tab to BookDetailsModal.vue
- [ ] Integrate BookReader component into Read tab
- [ ] Handle modal expansion for reading mode
- [ ] Update activeTab type to include 'read' option
- [ ] Add smooth transition between tabs
- [ ] Implement modal resize logic for reading view
- [ ] Add reading progress indicator in book card

## 📚 Phase 5: BookReader Component Enhancement

### Completed ✓
- [x] Create enhanced BookReader.vue component
- [x] Implement real page navigation with book content
- [x] Implement reading progress tracking and auto-save
- [x] Add loading and error states
- [x] Integrate with book content API
- [x] Track reading sessions (start/end)
- [x] Style book content (headings, images, code blocks)

### Tasks
- [ ] Integrate EPUB rendering with epubjs
- [ ] Integrate PDF rendering with pdfjs
- [ ] Add text selection handling for annotations
- [ ] Add page turn animations
- [ ] Implement keyboard navigation (arrow keys, space)
- [ ] Add touch gesture support for page turning
- [ ] Implement zoom functionality for PDFs
- [ ] Fix file import dialog (requires Electron dialog API)

## 🆕 Phase 6: New Component Creation

### Tasks
- [ ] Create ReaderSettingsModal.vue for reading preferences
- [ ] Create BookmarksPanel.vue for bookmark management
- [ ] Create AnnotationToolbar.vue for text selection actions
- [ ] Create AnnotationSidebar.vue for viewing all annotations
- [ ] Create ReadingProgress.vue for visual progress indicator
- [ ] Create TableOfContents.vue for chapter navigation
- [ ] Create SearchInBook.vue for in-book search functionality
- [ ] Create ReadingTimer.vue for tracking reading time

## 💾 Phase 7: State Management

### In Progress 🔄
- [ ] Create bookReaderStore.ts using Pinia

### Tasks
- [ ] Implement reading state persistence
- [ ] Add reading settings to existing settingsStore
- [ ] Create annotation store for managing highlights/notes
- [ ] Implement bookmark state management
- [ ] Add reading session tracking in store
- [ ] Create search results state management

## 🔥 Immediate Action Items (Critical Path)

### 1. File Import Dialog Implementation
```typescript
// In main process (books-api.ts or new handler)
ipcMain.handle('books:selectAndImportFile', async (event, bookId: number) => {
  const result = await dialog.showOpenDialog({
    properties: ['openFile'],
    filters: [
      { name: 'eBooks', extensions: ['epub', 'pdf', 'mobi', 'azw3', 'fb2', 'cbz'] }
    ]
  });
  
  if (!result.canceled && result.filePaths[0]) {
    return await bookContentApi.importFile(bookId, result.filePaths[0]);
  }
});
```

### 2. Create bookReaderStore.ts
```typescript
// Basic structure needed:
export const useBookReaderStore = defineStore('bookReader', {
  state: () => ({
    currentBook: null,
    currentPage: 1,
    totalPages: 0,
    chapters: [],
    bookmarks: [],
    annotations: [],
    readingSettings: {
      fontSize: 16,
      fontFamily: 'Georgia',
      lineHeight: 1.8,
      theme: 'light'
    }
  }),
  // ... actions and getters
});
```

### 3. PDF.js Integration
```bash
npm install pdfjs-dist@3.11.174
# Configure worker in vite.config.ts
# Create PDF renderer component
```

## 🖊️ Phase 8: Annotation System

### Tasks
- [ ] Implement annotation creation from text selection
- [ ] Create annotation-to-note conversion system
- [ ] Implement bookmark creation and management
- [ ] Add annotation rendering overlay
- [ ] Create highlight color picker
- [ ] Implement annotation export functionality
- [ ] Add annotation categories/tags
- [ ] Create annotation search functionality

## 🔄 Phase 9: Sync Integration

### Tasks
- [ ] Extend manifest types for book reading data
- [ ] Implement book content sync with chunking
- [ ] Add annotation/bookmark sync functionality
- [ ] Implement reading progress sync
- [ ] Add conflict resolution for annotations
- [ ] Create sync progress indicators
- [ ] Handle large book file sync optimization

## 🧪 Phase 10: Testing and Polish

### Tasks
- [ ] Test EPUB import and rendering
- [ ] Test PDF import and rendering
- [ ] Test annotation creation and persistence
- [ ] Test reading progress tracking
- [ ] Test sync functionality for book content
- [ ] Performance optimization for large books
- [ ] Add error handling and user feedback
- [ ] Test keyboard shortcuts and navigation
- [ ] Test mobile/touch interactions
- [ ] Add loading states and progress indicators

## 🎯 Additional Features

### Tasks
- [ ] Add reading statistics (words per minute, time spent)
- [ ] Implement dictionary/translation integration
- [ ] Add note-taking mode alongside reading
- [ ] Create reading goals and tracking
- [ ] Add export options for annotations
- [ ] Implement text-to-speech functionality
- [ ] Add reading themes (sepia, dark, etc.)
- [ ] Create reading history tracking
- [ ] Add social features (share quotes, progress)
- [ ] Implement offline reading mode

## 🐛 Known Issues to Fix

### Tasks
- [ ] Handle encrypted PDF files
- [ ] Fix memory leaks with large books
- [ ] Optimize rendering performance
- [ ] Handle malformed EPUB files gracefully
- [ ] Fix text selection in complex layouts
- [ ] Handle RTL (right-to-left) text properly
- [ ] Fix zoom issues in PDF viewer
- [ ] Handle books with missing metadata

## 📝 Documentation

### Tasks
- [ ] Create user guide for book reading features
- [ ] Document API endpoints for book content
- [ ] Write developer documentation for extending formats
- [ ] Create troubleshooting guide
- [ ] Document keyboard shortcuts
- [ ] Add inline help tooltips
- [ ] Create video tutorials