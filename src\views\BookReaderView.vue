<template>
  <div class="book-reader-view">
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>Loading book...</p>
    </div>
    <div v-else-if="error" class="error-container">
      <p class="error-message">{{ error }}</p>
      <button @click="goBack" class="back-button">Go Back</button>
    </div>
    <div v-else-if="book" class="reader-container">
      <div class="reader-header">
        <button @click="goBack" class="back-button">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path d="M10 12L6 8L10 4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          Back to Books
        </button>
        <h1 class="book-title">{{ book.title }}</h1>
        <div class="spacer"></div>
      </div>
      <BookReader :book="book" :expanded="true" @toggle-expanded="handleToggleExpanded" />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { BookWithNoteCount } from '../types/electron-api'
import BookReader from '../components/BookReader.vue'

export default defineComponent({
  name: 'BookReaderView',
  components: {
    BookReader
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    
    const book = ref<BookWithNoteCount | null>(null)
    const loading = ref(true)
    const error = ref<string>('')

    const loadBook = async () => {
      try {
        loading.value = true
        error.value = ''
        
        const bookId = parseInt(route.params.bookId as string)
        if (isNaN(bookId)) {
          error.value = 'Invalid book ID'
          return
        }

        // Load the book from the database
        const loadedBook = await window.db.books.getById(bookId)
        if (!loadedBook) {
          error.value = 'Book not found'
          return
        }

        // Get note count for the book
        const notes = await window.db.notes.getAll()
        const bookNotes = notes.filter(note => note.book_id === bookId)
        
        book.value = {
          ...loadedBook,
          notesCount: bookNotes.length
        }
      } catch (err) {
        console.error('Failed to load book:', err)
        error.value = 'Failed to load book. Please try again.'
      } finally {
        loading.value = false
      }
    }

    const goBack = () => {
      router.push({ name: 'Books' })
    }

    const handleToggleExpanded = (expanded: boolean) => {
      // Handle expansion if needed
      console.log('Reader expanded:', expanded)
    }

    onMounted(() => {
      loadBook()
    })

    return {
      book,
      loading,
      error,
      goBack,
      handleToggleExpanded
    }
  }
})
</script>

<style scoped>
.book-reader-view {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--color-bg-primary);
}

.loading-container,
.error-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-border-primary);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  color: var(--color-error);
  font-size: 16px;
  margin: 0;
}

.reader-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.reader-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border-primary);
  background-color: var(--color-bg-secondary);
  flex-shrink: 0;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: 1px solid var(--color-btn-secondary-border);
  border-radius: 8px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.back-button:hover {
  background-color: var(--color-btn-secondary-hover);
}

.book-title {
  margin: 0 16px;
  font-size: 20px;
  font-weight: 600;
  color: var(--color-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 400px;
}

.spacer {
  flex: 1;
}

@media (max-width: 768px) {
  .reader-header {
    padding: 8px 12px;
  }
  
  .book-title {
    font-size: 18px;
    max-width: 200px;
    margin: 0 8px;
  }
  
  .back-button {
    padding: 6px 12px;
    font-size: 12px;
  }
}
</style>
