# Noti Codebase: Comprehensive Analysis and Architectural Guide

**Document Version**: 1.0
**Analysis Date**: July 17, 2025

## 1. Introduction

This document provides an exhaustive analysis of the Noti application's codebase, covering both the Electron backend and the Vue.js frontend. It is intended to serve as a single source of truth for developers, outlining the application's architecture, conventions, and the current state of its features, with a special focus on the new **Book Reading** functionality. Every major claim is supported by a citation pointing to the relevant file and, where applicable, a line number or code block.

## 2. High-Level Architecture

The Noti application follows a modern, robust, and secure architecture for an Electron/Vue application.

-   **Process Model**: The application is split into two main processes, which is standard for Electron:
    -   **Main Process (`electron/main/`)**: A Node.js environment with full access to system resources. It manages the application lifecycle, database, filesystem, and all backend logic.
    -   **Renderer Process (`src/`)**: A standard web environment where the Vue.js user interface runs. It is sandboxed for security.
-   **Communication**: Communication between the two processes is handled securely via Electron's `contextBridge` and `ipc<PERSON><PERSON><PERSON>`/`ipcMain` modules. This prevents the frontend from directly accessing powerful Node.js APIs.
    -   **Citation**: `electron/preload/index.ts` uses `contextBridge.exposeInMainWorld` to securely expose a well-defined API to the renderer.
-   **Modularity**: The backend is highly modular. Logic is separated by domain into distinct API files (e.g., `notes-api.ts`, `books-api.ts`, `book-content-api.ts`), which are all aggregated and registered in a central `ipc-handlers.ts` file. This makes the codebase easy to navigate, maintain, and extend.
    -   **Citation**: The `initializeIpcHandlers` function in `electron/main/ipc-handlers.ts` calls multiple `register...Handlers` functions, demonstrating this modular registration pattern.

## 3. Backend Deep Dive (`electron/`)

The backend is professionally engineered, with a clear separation of concerns and a focus on data integrity and security.

### 3.1. Database (`electron/main/database/`)

-   **Engine**: SQLite, configured to use WAL (Write-Ahead Logging) mode for improved concurrency and performance.
    -   **Citation**: `electron/main/database/database.ts`, `setupDatabaseConfig` function: `await runAsync(db, 'PRAGMA journal_mode = WAL;');`
-   **Schema**: The schema is comprehensive and well-designed, using foreign keys with `ON DELETE CASCADE` and `ON DELETE SET NULL` to ensure relational integrity.
    -   **Citation**: `electron/main/database/database.ts`, `createAllTables` function.
-   **Migrations**: A simple but robust migration system is in place. The `handleDatabaseMigrations` function uses `ALTER TABLE` within `try...catch` blocks to add new columns, preventing crashes if a migration has already run.
    -   **Citation**: `electron/main/database/database.ts`, `handleDatabaseMigrations` function.
-   **Data Access Layer**: The `database-api.ts` file provides a clean, promise-based API for all CRUD operations, abstracting the raw SQL queries from the business logic.
    -   **Citation**: `electron/main/database/database-api.ts`.

### 3.2. API Layer (`electron/main/api/`)

-   **Convention**: Each feature domain (notes, folders, books, etc.) has its own dedicated API file. These files contain the core business logic for that domain.
-   **Error Handling**: Every IPC handler is wrapped in a `try...catch` block that re-throws the error, ensuring that any backend failure is properly propagated to the frontend for user feedback.
    -   **Citation**: This pattern is used consistently across all `register...Handlers` functions in files like `electron/main/api/annotation-api.ts`.
-   **Transactions**: The codebase demonstrates an understanding of database transactions, although its application is inconsistent. The bookmark reordering logic is correctly wrapped in a transaction, but other critical multi-step operations are not.
    -   **Citation (Good Practice)**: `electron/main/api/annotation-api.ts`, `bookmarks:reorder` handler: `await runAsync(db, 'BEGIN TRANSACTION');`
    -   **Citation (Needs Improvement)**: `electron/main/api/annotation-api.ts`, `annotations:convert-to-note` handler. This multi-step operation lacks a transaction wrapper.

### 3.3. Security and Utilities (`electron/utils/`)

-   **Filename Sanitization**: The application includes a robust, Unicode-safe utility for sanitizing filenames and folder names. It correctly handles reserved names on Windows and removes unsafe characters for cross-platform compatibility.
    -   **Citation**: `electron/utils/filename-sanitizer.ts`.
-   **Custom Protocols**: A custom `noti-media://` protocol is used to securely serve local media files to the sandboxed renderer, which is a security best practice.
    -   **Citation**: `electron/main/protocol-handlers.ts`.

## 4. Frontend Deep Dive (`src/`)

The frontend is built with Vue.js and TypeScript, following modern development practices.

### 4.1. Views (`src/views/`)

-   **Structure**: The application is divided into clear, top-level views for each major feature area: `DashboardView`, `NotesView`, `FoldersView`, `BooksView`, `TimerView`, and `SettingsView`.
-   **Convention**: Views are responsible for fetching the initial data required for that screen and passing it down to child components. They handle the overall page layout.
-   **Book Reader View**: A dedicated `BookReaderView.vue` exists. Its role is to fetch the specific book requested by the URL parameter (`/books/read/:bookId`) and pass this data to the `BookReader.vue` component. This is a clean separation of concerns.
    -   **Citation**: `src/views/BookReaderView.vue`, `loadBook` function.

### 4.2. Components (`src/components/`)

-   **Convention**: Components are well-organized by feature (e.g., `components/books/`, `components/notes/`). They are generally responsible for a specific piece of UI and logic.
-   **Styling**: Scoped CSS is used extensively, and theming is achieved through CSS variables (e.g., `var(--color-primary)`), which is a flexible and maintainable approach.
    -   **Citation**: The `<style scoped>` blocks in `BookReader.vue` and other components.

### 4.3. State Management

-   **Convention**: The application uses Pinia for centralized state management, as seen with `useTimerStore` and `useNotesViewStore`. This is a modern and recommended practice for Vue applications.
-   **Gap**: The Book Reading feature currently lacks a dedicated Pinia store, with its state being managed locally within the `BookReader.vue` component. This is a key issue to be addressed.
    -   **Citation**: `src/components/BookReader.vue`, `setup()` function, where state like `currentPage` and `totalPages` is defined with `ref()`.

## 5. Book Reading Feature: Current State Analysis

The implementation is approximately **70% complete**. The backend is a robust and feature-rich foundation, while the frontend is a well-designed but non-functional shell.

### 5.1. What is Implemented and Working

-   **Database Schema**: All 8 new tables (`book_content`, `book_annotations`, etc.) and the modifications to the `books` and `notes` tables are correctly implemented in `database.ts`.
-   **Backend APIs**: All required backend logic for parsing books, managing content, creating/deleting annotations and bookmarks, and performing full-text search is complete and exposed via IPC handlers.
-   **API Bridge**: The `electron-api.d.ts` and `api-bridge.ts` files correctly and securely expose the entire backend API surface to the frontend with full TypeScript support.
-   **Basic UI Shell**: The `BookReader.vue` component exists with a polished UI, including headers, buttons, and placeholders. It correctly handles the lifecycle of a reading session (starting on mount, ending on unmount).
    -   **Citation**: `src/components/BookReader.vue`, `onMounted` and `onUnmounted` hooks.

### 5.2. Critical Flaws and Missing Components

1.  **Architectural Flaw: BLOB Storage**: Book files are stored directly in the SQLite database as BLOBs. This is a major performance and scalability risk that can lead to a bloated and slow database.
    -   **Citation**: `electron/main/database/database.ts`, `CREATE TABLE IF NOT EXISTS book_content`, the `original_file BLOB` column.

2.  **Security Flaw: No HTML Sanitization**: EPUB files can contain malicious scripts. The current parser does not sanitize the HTML content before it is stored and potentially rendered with `v-html`.
    -   **Citation**: `electron/main/lib/book-parser.ts`. The `parseEpub` method does not show any call to a sanitization library like DOMPurify.

3.  **Frontend Blocker: File Import**: The UI has a file input, but the `processBookFile` function in `BookReader.vue` contains a `// TODO` and an `alert()`, confirming it cannot pass the file path to the backend due to browser security restrictions.
    -   **Citation**: `src/components/BookReader.vue`, `processBookFile` function.

4.  **Frontend Blocker: Content Rendering**: The component uses `v-html` to render content, which cannot display a PDF or a properly paginated EPUB. Integration with `PDF.js` or `epub.js` is missing.
    -   **Citation**: `src/components/BookReader.vue`, `<div v-else-if="currentPageContent" class="page-content" v-html="currentPageContent"></div>`.

5.  **Frontend Architecture: Missing State Store**: All state is local to `BookReader.vue`, which will make it difficult to build out the UI with shared state between components like sidebars and modals.
    -   **Citation**: `src/components/BookReader.vue`, `setup()` function.

## 6. Actionable Recommendations

Based on this comprehensive audit, the path forward is clear. The following steps, in this order, will lead to the successful completion of the feature.

1.  **Remediate Backend Flaws**: Before adding new features, the foundational issues must be fixed.
    -   **Task 1**: Refactor the book content storage to use the filesystem instead of BLOBs.
    -   **Task 2**: Integrate an HTML sanitization library (e.g., DOMPurify) into the EPUB parsing logic in `book-parser.ts`.
    -   **Task 3**: Review and add database transactions to all multi-step write operations in `annotation-api.ts` and `book-content-api.ts` to guarantee data integrity.

2.  **Implement Frontend MVP**: Focus on making the reader functional.
    -   **Task 4**: Implement the Electron `dialog.showOpenDialog` IPC handler for file import and connect it to the UI.
    -   **Task 5**: Integrate `PDF.js` into a `ReadingContent.vue` component to correctly render PDF files.
    -   **Task 6**: Create a `bookReaderStore.ts` using Pinia and refactor `BookReader.vue` to use this central store.

3.  **Build Out Features**: Once the MVP is functional, build the remaining UI components as designed in `book-reading-ui-architecture.md`.

This detailed analysis provides a complete picture of the project. The foundation is strong, and with these targeted actions, the book reading feature can be successfully and robustly completed.
